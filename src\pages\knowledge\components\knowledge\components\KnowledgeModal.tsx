import {
  useState,
  useRef,
  useImperativeHandle,
  useMemo,
  useReducer,
  forwardRef,
  useCallback,
  CSSProperties,
} from 'react';
import {
  Button,
  Divider,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  Image,
  InputTag,
} from '@arco-design/web-react';
import {
  useCreateKnowledge,
  useUpdateKnowledge,
} from '../hooks/knowledge-hooks';
import { getKbDetail } from '../services/knowledge-service';
import IconKnowledge from '@/assets/knowledge/IconKnowledge.png';
import KnowledgeMemberModal from './KnowledgeMemberModal';
import { IconPlus } from '@arco-design/web-react/icon';

const Option = Select.Option;
const FormItem = Form.Item;

// 新增/修改知识库
const KnowledgeModal = forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false);
  // const [tags, setTags] = useState<string[]>([]);

  const { loading: createLoading, createKnowledge } = useCreateKnowledge();

  const { saveKnowledgeConfiguration, loading: updateLoading } =
    useUpdateKnowledge(true);

  const [form] = Form.useForm();

  const permission = Form.useWatch('permission', form);

  const id = Form.useWatch('id', form);

  const confirmLoading = useMemo(
    () => (id ? updateLoading : createLoading),
    [id, createLoading]
  );

  const [tags, dispatchTag] = useReducer((state, action) => {
    switch (action.type) {
      case 'add':
        return [...state, `标签${state.length + 1}`];
      case 'remove':
        const _state = state.slice();
        _state.splice(action.index, 1);
        return _state;
    }
  }, []);

  const onOpen = async (row?: any) => {
    form.resetFields();
    if (row) {
      const { id } = row;
      const {
        data: { data: knowledgeDetails },
      } = await getKbDetail(id);

      form.setFieldsValue(knowledgeDetails);
    }
    setVisible(true);
  };

  useImperativeHandle(ref, () => ({
    open: onOpen,
  }));

  const title = useMemo<string>(() => {
    return id ? '修改库' : '创建库';
  }, [id]);

  const KnowledgeMemberModalRef = useRef<{
    open: () => void;
  }>();

  const hideModal = useCallback(() => {
    setVisible(false);
  }, []);

  const handleOk = async () => {
    const ret = await form.validate();
    const { id, ...values } = ret;
    if (id) {
      await saveKnowledgeConfiguration({
        ...values,
        kb_id: id,
      });
    } else {
      await createKnowledge(values);
    }
    hideModal();
  };

  return (
    <Modal
      title={title}
      visible={visible}
      cancelText="取消"
      okText="创建"
      alignCenter={false}
      className="min-w-[640px] [&_.arco-modal-content]:p-[24px] top-[5%]"
      onCancel={() => setVisible(false)}
      confirmLoading={confirmLoading}
      onOk={handleOk}
    >
      <Form form={form} layout="vertical" autoComplete="off">
        <FormItem field="id" hidden>
          <Input hidden name="id" />
        </FormItem>
        <FormItem field="parser_id" hidden>
          <Input hidden name="parser_id" />
        </FormItem>
        {/* 名称 */}
        <div className="grid grid-cols-[auto_auto_1fr] colum mb-[24px]">
          <Image
            className="shrink-0"
            src={IconKnowledge}
            width={72}
            height={72}
            preview={false}
          />
          <Divider className="h-full mx-[24px]" type="vertical" />
          <FormItem
            className="mb-0"
            label="名称"
            field="name"
            required
            rules={[
              {
                required: true,
                message: '请输入名称',
              },
            ]}
          >
            <Input placeholder="请输入" />
          </FormItem>
        </div>
        {/* 描述 */}
        <FormItem label="描述" field="description">
          <Input.TextArea
            className="min-h-[64px] resize-none"
            placeholder="请输入"
            maxLength={100}
            showWordLimit
          />
        </FormItem>
        {/* 标签 */}
        <FormItem
          label={
            <div className="flex items-center justify-between">
              <div>
                <span>标签</span>
                <span className="text-[#adadad] text-[12px]">
                  （标签至多添加3个）
                </span>
              </div>
            </div>
          }
          rules={[
            {
              validator: (value, cb) => {
                if (value?.length > 3) {
                  return cb('最多添加3个标签');
                }
                return cb();
              },
            },
          ]}
          field="tags"
        >
          <InputTag saveOnBlur placeholder="请输入" allowClear />
        </FormItem>
        {/* 语言 */}
        <FormItem label="语言" field="language" initialValue="zh-CN">
          <Select>
            <Option value={'zh-CN'}>中文</Option>
            <Option value={'en'}>英文</Option>
          </Select>
        </FormItem>
        {/* 权限 */}
        <FormItem label="权限">
          <FormItem field="permission" initialValue={'0'}>
            <Select>
              <Option value={'0'}>公开</Option>
              <Option value={'1'}>部分成员可访问</Option>
            </Select>
          </FormItem>
          {/* {permission === '1' && (
            <div className="rounded-[8px] border border-[#f0f0f0] p-[4px] flex gap-[4px] flex-wrap">
              <ul>
                <li>
                  <Tag
                    className="h-[32px] text-[#464646] [&_.arco-tag-close-btn]:text-[#969696] rounded-[6px] bg-[#fafafa]"
                    closable
                    bordered
                  >
                    产品
                  </Tag>
                </li>
              </ul>
              <Button
                className="size-[32px] text-[#969696] border [&:not(:hover)]:bg-[#fafafa] border-[#ebebeb] rounded-[6px]"
                type="text"
                icon={<IconPlus />}
                onClick={() => KnowledgeMemberModalRef.current.open()}
              ></Button>
              <KnowledgeMemberModal ref={KnowledgeMemberModalRef} />
            </div>
          )} */}
        </FormItem>
      </Form>
    </Modal>
  );
});

export default KnowledgeModal;
