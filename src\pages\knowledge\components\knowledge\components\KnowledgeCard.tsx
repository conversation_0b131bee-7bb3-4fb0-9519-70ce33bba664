import {
  Card,
  Image,
  Switch,
  Typography,
  Popover,
  Button,
} from '@arco-design/web-react';
import IconKnowledge from '@/assets/knowledge/IconKnowledge.png';
import { IconMore } from '@arco-design/web-react/icon';
import { useEffect, useState } from 'react';
import type React from 'react';

const { Text } = Typography;

export type Action = {
  label: string;
  className?: string;
  type: 'start' | 'del' | 'edit' | 'enable' | 'disable';
};

interface KnowledgeCardProps {
  tag: React.ReactNode;
  footerTag: React.ReactNode;
  actionButtons: Action[];
  description: string;
  title: string;
  createTime: string;
  handleAction: (type: Action['type']) => void;
  handleSwitch: (value: boolean) => void;
  onClick?: () => void;
}

const KnowledgeCard = ({
  tag,
  footerTag,
  handleAction,
  actionButtons,
  description,
  title,
  createTime,
  handleSwitch,
  onClick,
}: KnowledgeCardProps) => {
  const [actionPopoverVisible, setActionPopoverVisible] = useState(false);

  const _handleAction = (
    type: (typeof actionButtons)[number]['type'],
    e: Event
  ) => {
    e.stopPropagation();
    setActionPopoverVisible(false);
    handleAction(type);
  };

  return (
    <Card
      className={`rounded-[8px] cursor-pointer group/card h-full ${
        actionPopoverVisible ? 'shadow-[0_4px_10px_rgb(var(--gray-2))]' : ''
      }`}
      hoverable
      bordered
      onClick={onClick}
    >
      <div className="flex justify-between items-center">
        <div>
          <Image src={IconKnowledge} width={48} height={48} preview={false} />
          <Text className="text-[16px]  ml-[12px]">{title}</Text>
        </div>
        {/* <Switch onChange={handleSwitch} /> */}
      </div>
      <p className="my-[8px] text-[#5c5c5c] leading-[24px] min-h-[24px] text-ellipsis overflow-hidden">
        {description}
      </p>
      <div className="min-h-[80px] flex gap-[8px]">{tag}</div>
      <div className="flex justify-end items-center relative">
        <div
          className={`group-hover/card:opacity-100 flex opacity-0 justify-between transition-opacity items-center flex-1 absolute w-full z-1 ${
            actionPopoverVisible ? 'opacity-100' : ''
          }`}
        >
          <Text className="text-[#adadad] text-[12px]">
            创建时间：{createTime}
          </Text>
          <Popover
            position="bl"
            trigger="hover"
            popupVisible={actionPopoverVisible}
            onVisibleChange={setActionPopoverVisible}
            className="[&_.arco-popover-content]:p-[8px]"
            content={actionButtons.map((action) => (
              <Button
                key={action.type}
                type="text"
                long={true}
                className={`min-w-[144px] text-left ${action.className}`}
                onClick={(e) => _handleAction(action.type, e)}
              >
                {action.label}
              </Button>
            ))}
            // getPopupContainer={(node: HTMLElement) => node}
          >
            <Button
              type="text"
              className="rounded-[8px] border border-[#f5f5f5]"
              icon={<IconMore />}
            ></Button>
          </Popover>
        </div>
        <div
          className={`group-hover/card:opacity-0 ${
            actionPopoverVisible ? 'opacity-0' : 'opacity-100'
          }`}
        >
          {footerTag}
        </div>
      </div>
    </Card>
  );
};

export default KnowledgeCard;
