import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Navigate, useNavigate, useLocation } from 'react-router-dom';
import { Layout, Menu, Breadcrumb, Spin } from '@arco-design/web-react';
import { IconMenuFold, IconMenuUnfold, IconRight } from '@arco-design/web-react/icon';
import { useSelector } from 'react-redux';
import qs from 'query-string';
import NProgress from 'nprogress';
import useRoute, { IRoute } from '@/routes';
import { isArray } from './utils/is';
import useLocale from './utils/useLocale';
import getUrlParams from './utils/getUrlParams';
import lazyload from './utils/lazyload';
import { GlobalState } from './store';
import styles from './style/layout.module.less';
import Logo from '@/assets/Agent foundry-logo.svg';
import IconApplicationAgent from '@/assets/IconApplicationAgent.svg';
import IconApplicationAgentSelected from '@/assets/IconApplicationAgentSelected.svg';
import IconApplicationArtifact from '@/assets/IconApplicationArtifact.svg';
import IconApplicationArtifactSelected from '@/assets/IconApplicationArtifactSelected.svg';
import IconApplicationWorkflow from '@/assets/IconApplicationWorkflow.svg';
import IconApplicationWorkflowSelected from '@/assets/IconApplicationWorkflowSelected.svg';
import IconDashboardSelected from '@/assets/IconDashboardSelected.svg';
import IconDashboard from '@/assets/IconDashboard.svg';
import IconApplication from '@/assets/IconApplication.svg';
import IconApplicationSelected from '@/assets/IconApplicationSelected.svg';
import IconKnowledge from '@/assets/IconKnowledge.svg';
import IconKnowledgeSelected from '@/assets/IconKnowledgeSelected.svg';
import IconModel from '@/assets/IconModel.svg';
import IconModelSelected from '@/assets/IconModelSelected.svg';
import IconPlugin from '@/assets/IconPlugin.svg';
import IconPluginSelected from '@/assets/IconPluginSelected.svg';
import IconSdk from '@/assets/IconSdk.svg';
import IconSdkSelected from '@/assets/IconSdkSelected.svg';
import IconUsage from '@/assets/IconUsage.svg';
import IconUsageSelected from '@/assets/IconUsageSelected.svg';
import IconAiaction from '@/assets/IconAiaction.svg';
import IconAiactionSelected from '@/assets/IconAiactionSelected.svg';
import IconAcp from '@/assets/IconAcp.svg';
import IconAcpSelected from '@/assets/IconAcpSelected.svg';
import IconEnterprise from '@/assets/enterprise.svg';
import IconEnterprises from '@/assets/IconEnterprises.svg';
import IconUser from '@/assets/IconUser.svg';
import IconMore from '@/assets/iconMore.svg';
import DefaultAvatar from '@/assets/user/DefaultAvatar.png';
import './style/global.css';
import SettingDropdownMenu from '@/components/Sidebar/SettingDropdownMenu';

const MenuItem = Menu.Item;
const SubMenu = Menu.SubMenu;
const Sider = Layout.Sider;
const Content = Layout.Content;

// 添加辅助函数检查头像URL是否有效
const isValidAvatarUrl = (url: string | null | undefined): boolean => {
  // 如果URL为空，则无效
  if (!url) return false;

  // 检查不完整的路径，如 "/user/avatar"
  if (url === '/user/avatar') return false;

  // 检查URL是否是有效的HTTP、HTTPS URL或data URL
  return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:');
};

function getIconFromKey(key, selected) {
  // 对于动态路由，提取基础路径进行匹配
  const getBaseKey = (routeKey) => {
    // 处理动态路由，移除ID部分
    // 例如：acp/serverdetail/d85533fe-707a-4110-bcd4-3fafc3b99942 -> acp/serverdetail
    const parts = routeKey.split('/');
    
    // 检查是否是已知的动态路由模式
    if (parts.length === 3 && parts[0] === 'acp' && parts[1] === 'servers') {
      return 'acp/servers';
    }
    
    // 可以在这里添加其他动态路由模式的处理
    // 例如：agent/info/id -> agent/info
    if (parts.length === 3 && parts[0] === 'agent' && parts[1] === 'info') {
      return 'agent/info';
    }
    
    if (parts.length === 3 && parts[0] === 'application' && parts[1] === 'info') {
      return 'application/info';
    }
    
    if (parts.length === 3 && parts[0] === 'model' && parts[1] === 'detail') {
      return 'model/detail';
    }
    
    if (parts.length === 3 && parts[0] === 'plugin' && parts[1] === 'detail') {
      return 'plugin/detail';
    }
    
    if (parts.length === 3 && parts[0] === 'sdk' && parts[1] === 'detail') {
      return 'sdk/detail';
    }
    
    if (parts.length === 3 && parts[0] === 'usage' && parts[1] === 'detail') {
      return 'usage/detail';
    }
    
    if (parts.length === 3 && parts[0] === 'workflow' && parts[1] === 'detail') {
      return 'workflow/detail';
    }
    
    // 处理timeSequenceCard的动态路由: timeSequenceCard/cards/id -> timeSequenceCard/cards
    if (parts.length === 3 && parts[0] === 'timeSequenceCard' && parts[1] === 'cards') {
      return 'timeSequenceCard/cards';
    }
    
    // 如果不是已知的动态路由，返回原始key
    return routeKey;
  };

  const baseKey = getBaseKey(key);
  
  switch (baseKey) {
    case 'dashboard':
      return selected ? (
        <IconDashboardSelected className={styles.icon} />
      ) : (
        <IconDashboard className={styles.icon} />
      );
    case 'application':
    case 'application/list':
    case 'application/create':
    case 'application/info':
      return selected ? (
        <IconApplicationSelected className={styles.icon} />
      ) : (
        <IconApplication className={styles.icon} />
      );
    case 'agent':
    case 'agent/list':
    case 'agent/create':
    case 'agent/info':
    case 'application/agent':
    case 'application/agent/list':
    case 'application/agent/create':
    case 'application/agent/info':
      return selected ? (
        <IconApplicationAgentSelected className={styles.icon} />
      ) : (
        <IconApplicationAgent className={styles.icon} />
      );
    case 'timeSequenceCard':
    case 'timeSequenceCard/create':
    case 'timeSequenceCard/cards':
      return selected ? (
        <IconApplicationArtifactSelected className={styles.icon} />
      ) : (
        <IconApplicationArtifact className={styles.icon} />
      );
    case 'workflow':
    case 'workflow/detail':
    case 'application/workflow':
    case 'application/workflow/detail':
      return selected ? (
        <IconApplicationWorkflowSelected className={styles.icon} />
      ) : (
        <IconApplicationWorkflow className={styles.icon} />
      );
    case 'aiaction':
    case 'aiaction/create':
      return selected ? (
        <IconAiactionSelected className={styles.icon} />
      ) : (
        <IconAiaction className={styles.icon} />
      );
    case 'acp':
    case 'acp/servers':
      return selected ? (
        <IconAcpSelected className={styles.icon} />
      ) : (
        <IconAcp className={styles.icon} />
      );
    case 'model':
    case 'model/create':
    case 'model/detail':
    case 'model/configdetail':
    case 'model/configcreate':
      return selected ? (
        <IconModelSelected className={styles.icon} />
      ) : (
        <IconModel className={styles.icon} />
      );
    case 'knowledge':
    case 'knowledge/list':
    case 'knowledge/list/detail':
      return selected ? (
        <IconKnowledgeSelected className={styles.icon} />
      ) : (
        <IconKnowledge className={styles.icon} />
      );
    case 'plugin':
    case 'plugin/detail':
      return selected ? (
        <IconPluginSelected className={styles.icon} />
      ) : (
        <IconPlugin className={styles.icon} />
      );
    case 'sdk':
    case 'sdk/detail':
      return selected ? (
        <IconSdkSelected className={styles.icon} />
      ) : (
        <IconSdk className={styles.icon} />
      );
    case 'usage':
    case 'usage/detail':
      return selected ? (
        <IconUsageSelected className={styles.icon} />
      ) : (
        <IconUsage className={styles.icon} />
      );
    case 'user':
      return selected ? (
        <IconUser className={styles.icon} />
      ) : (
        <IconUser className={styles.icon} />
      );
    case 'enterprise':
      return selected ? (
        <IconEnterprises className={styles.icon} />
      ) : (
        <IconEnterprises className={styles.icon} />
      );
    default:
      return <div className={styles['icon-empty']} />;
  }
}
//通过递归遍历路由配置，动态加载组件并生成扁平化的路由数组，适用于需要动态加载和扁平化路由的场景。
function getFlattenRoutes(routes) {
  const mod = import.meta.glob('./pages/**/[a-z[]*.tsx');
  const res = [];
  function travel(_routes) {
    _routes.forEach((route) => {
      if (route.hasShow) {
        try {
          route.component = lazyload(mod[`./pages/${route.key}/index.tsx`]);
          res.push(route);
        } catch (e) {
          console.log(`Error loading route with key: ${route.key}`);
          console.error(e);
        }
        // 由于已经处理了当前路由，不需要再处理子路由
        //return;
      }

      const visibleChildren = (route.children || []).filter(
        (child) => !child.ignore
      );
      if (route.key && (!route.children || !visibleChildren.length)) {
        try {
          route.component = lazyload(mod[`./pages/${route.key}/index.tsx`]);
          res.push(route);
        } catch (e) {
          console.log(route.key);
          console.error(e);
        }
      }

      if (isArray(route.children) && route.children.length) {
        travel(route.children);
      }
    });
  }
  travel(routes);
  return res;
}

function PageLayout() {
  const urlParams = getUrlParams();
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  const currentComponent = qs.parseUrl(pathname).url.slice(1);
  const locale = useLocale();
  const { settings, userLoading, userInfo } = useSelector((state: GlobalState) => state);
  const breadcrumbMenuName = useSelector((state: GlobalState) => state.breadcrumbMenuName);
  const [routes, defaultRoute] = useRoute(userInfo?.permissions);
  const defaultSelectedKeys = [currentComponent || defaultRoute];
  const paths = (currentComponent || defaultRoute).split('/');
  const defaultOpenKeys = paths.slice(0, paths.length - 1);
  const [breadcrumb, setBreadCrumb] = useState([]);
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>(defaultSelectedKeys);
  const [openKeys, setOpenKeys] = useState<string[]>(defaultOpenKeys);
  const routeMap = useRef<Map<string, React.ReactNode[]>>(new Map());
  const menuMap = useRef<
    Map<string, { menuItem?: boolean; subMenu?: boolean }>
  >(new Map());
  const navbarHeight = 60;
  const menuWidth = collapsed ? 48 : settings.menuWidth;
  const showNavbar = settings.navbar && urlParams.navbar !== false;
  const showMenu = settings.menu && urlParams.menu !== false;
  const showFooter = settings.footer && urlParams.footer !== false;
  const flattenRoutes = useMemo(() => getFlattenRoutes(routes) || [], [routes]);


  // 渲染当前路由对应的组件
  function renderCurrentRoute() {
    // 如果是根路径，重定向到默认路由
    if (pathname === '/') {
      return <Navigate to={`/${defaultRoute}`} replace />;
    }

    // 使用原有的flattenRoutes逻辑，支持所有路由包括动态路由
    const matchedRoute = flattenRoutes
      .filter(route => {
        const routePath = `/${route.key}`;
        // 精确匹配主路由
        if (pathname === routePath) {
          return true;
        }
        // 匹配子路由 (支持动态路由参数)
        if (pathname.startsWith(routePath + '/')) {
          return true;
        }
        return false;
      })
      .sort((a, b) => {
        // 优先选择更精确匹配的路由（路径长度更长的）
        const aPath = `/${a.key}`;
        const bPath = `/${b.key}`;
        
        // 如果是精确匹配，优先选择
        if (pathname === aPath) return -1;
        if (pathname === bPath) return 1;
        
        // 按路径长度排序，优先选择更长的路径
        return b.key.length - a.key.length;
      })[0];

    if (matchedRoute && matchedRoute.component) {
      const Component = matchedRoute.component;
      return (
        <React.Suspense fallback={<div>Loading...</div>}>
          <Component />
        </React.Suspense>
      );
    }

    // 如果没有匹配的路由，显示 403 页面
    const Exception403 = lazyload(() => import('./pages/exception/403'));
    return <Exception403 />;
  }
  const [menuVisible, setMenuVisible] = useState(false);

  function onClickMenuItem(key) {
    const currentRoute = flattenRoutes.find((r) => r.key === key);
    if (currentRoute) {
      const component = currentRoute.component;
      NProgress.start();
      
      // 检查是否有preload方法（兼容性处理）
      if (component && typeof component.preload === 'function') {
        component.preload().then(() => {
          navigate(currentRoute.path ? currentRoute.path : `/${key}`);
          NProgress.done();
        });
      } else {
        // 直接导航，React.lazy会处理异步加载
        navigate(currentRoute.path ? currentRoute.path : `/${key}`);
        NProgress.done();
      }
    }
  }

  function onClickSubMenu(key) {
    // 忽略 key="user" 的跳转逻辑
    if (key === 'user') {
      return;
    }
    // 检查是否存在对应的路由
    const currentRoute = flattenRoutes.find((r) => r.key === key);
    if (currentRoute) {
      const component = currentRoute.component;
      NProgress.start();
      
      // 检查是否有preload方法（兼容性处理）
      if (component && typeof component.preload === 'function') {
        component.preload().then(() => {
          navigate(currentRoute.path ? currentRoute.path : `/${key}`);
          NProgress.done();
        });
      } else {
        // 直接导航，React.lazy会处理异步加载
        navigate(currentRoute.path ? currentRoute.path : `/${key}`);
        NProgress.done();
      }
    }
  }

  function toggleCollapse() {
    setCollapsed((collapsed) => !collapsed);
  }


  const paddingLeft = showMenu ? { paddingLeft: menuWidth } : {};
  const paddingTop = showNavbar ? { paddingTop: 8 } : {};
  const paddingStyle = { ...paddingLeft };

  // function renderRoutes(locale) {
  //   routeMap.current.clear();
  //   return function travel(_routes: IRoute[], level, parentNode = []) {
  //     return _routes.map((route) => {
  //       const { breadcrumb = true, ignore } = route;
  //       // const iconDom = getIconFromKey(route.key);
  //       // const titleDom = (
  //       //   <>
  //       //     {iconDom} {locale[route.name] || route.name}
  //       //   </>
  //       // );
  //       const isSelected = selectedKeys.includes(route.key);
  //       const iconDom = getIconFromKey(route.key, isSelected);

  //       const className = `custom-menu-div ${isSelected ? 'selected' : ''}`;
  //       const titleDom = (
  //         <div className={className} onClick={(event) => jumpPageClick(event, undefined != route.children, route.key)}>
  //           {iconDom} {locale[route.name] || route.name}
  //         </div>
  //       );

  //       routeMap.current.set(
  //         `/${route.key}`,
  //         breadcrumb ? [...parentNode, route.name] : []
  //       );

  //       const visibleChildren = (route.children || []).filter((child) => {
  //         const { ignore, breadcrumb = true } = child;
  //         if (ignore || route.ignore) {
  //           routeMap.current.set(
  //             `/${child.key}`,
  //             breadcrumb ? [...parentNode, route.name, child.name] : []
  //           );
  //         }

  //         return !ignore;
  //       });

  //       if (ignore) {
  //         return '';
  //       }
  //       if (visibleChildren.length) {
  //         menuMap.current.set(route.key, { subMenu: true });
  //         // 增加父级路由规则，关键参数：hasShow
  //         if (route.hasShow) {
  //           menuMap.current.set(route.key, { menuItem: true });
  //         }
  //         return (
  //           <SubMenu key={route.key} title={titleDom}>
  //             {travel(visibleChildren, level + 1, [...parentNode, route.name])}
  //           </SubMenu>
  //         );
  //       }
  //       menuMap.current.set(route.key, { menuItem: true });
  //       return <MenuItem key={route.key}>{titleDom}</MenuItem>;
  //     });
  //   };
  // }

  function findRouteByName(routes, routeName: string) {
    for (const rs of routes) {
      if (rs.name === routeName) {
        return rs;
      }
      // 如果有 children 字段，继续递归查找
      if (rs.children) {
        const foundInChildren = findRouteByName(rs.children, routeName);
        if (foundInChildren) {
          return foundInChildren;
        }
      }
    }
    return undefined;
  }

  function renderRoutes(locale) {
    routeMap.current.clear();
    return function travel(_routes, level, parentNode = []) {
      return _routes.map((route) => {
        const { breadcrumb = true, ignore } = route;
        const isSelected = selectedKeys.includes(route.key);
        const iconDom = getIconFromKey(route.key, isSelected);
        const className = collapsed
          ? `custom-menu-div-collapsed ${isSelected ? 'selected' : ''}`
          : `custom-menu-div ${isSelected ? 'selected' : ''}`;
        const titleDom = (
          <div className={className}>
            {iconDom} {!collapsed && (locale[route.name] || route.name)}
          </div>
        );

        // 设置当前节点的routeMap，无论是否被忽略
        routeMap.current.set(
          `/${route.key}`,
          breadcrumb ? [...parentNode, route.name] : []
        );
        // 处理所有子节点，无论是否被忽略
        const children = route.children || [];
        children.forEach((child) => {
          // 递归处理子节点，确保设置其routeMap
          travel(
            [child],
            level + 1,
            breadcrumb ? [...parentNode, route.name] : parentNode
          );
        });

        if (ignore) {
          return '';
        }

        // 生成菜单时过滤被忽略的子节点
        const visibleChildren = children.filter((child) => !child.ignore);

        if (visibleChildren.length) {
          menuMap.current.set(route.key, { subMenu: true });
          if (route.hasShow) {
            menuMap.current.set(route.key, { menuItem: true });
          }
          return (
            <SubMenu key={route.key} title={titleDom}>
              {travel(visibleChildren, level + 1, [...parentNode, route.name])}
            </SubMenu>
          );
        }
        menuMap.current.set(route.key, { menuItem: true });
        return <MenuItem key={route.key}>{titleDom}</MenuItem>;
      });
    };
  }

  // function updateMenuStatus() {
  //   // const pathKeys = pathname.split('/');
  //   // const newSelectedKeys: string[] = [];
  //   // const newOpenKeys: string[] = [...openKeys];
  //   // while (pathKeys.length > 0) {
  //   //   const currentRouteKey = pathKeys.join('/');
  //   //   const menuKey = currentRouteKey.replace(/^\//, '');
  //   //   const menuType = menuMap.current.get(menuKey);
  //   //   if (menuType && menuType.menuItem) {
  //   //     newSelectedKeys.push(menuKey);
  //   //   }
  //   //   if (menuType && menuType.subMenu && !openKeys.includes(menuKey)) {
  //   //     newOpenKeys.push(menuKey);
  //   //   }
  //   //   pathKeys.pop();
  //   // }
  //   const pathKeys = pathname;
  //   const newSelectedKeys: string[] = [];
  //   const newOpenKeys: string[] = [...openKeys];
  //   if (pathKeys) {
  //     const menuKey = pathKeys.replace(/^\//, '');
  //     const menuType = menuMap.current.get(menuKey);
  //     if (menuType && menuType.menuItem) {
  //       newSelectedKeys.push(menuKey);
  //     }
  //     if (menuType && menuType.subMenu && !openKeys.includes(menuKey)) {
  //       newOpenKeys.push(menuKey);
  //     }
  //   }
  //   setSelectedKeys(newSelectedKeys);
  //   setOpenKeys(newOpenKeys);
  // }

  function updateMenuStatus() {
    const pathKeys = pathname.split('/');
    const newSelectedKeys: string[] = [];
    const newOpenKeys: string[] = [...openKeys];
    let currentPath = '';
    
    pathKeys.forEach((key) => {
      if (key) {
        currentPath += `/${key}`;
        const menuKey = currentPath.replace(/^\//, '');
        const menuType = menuMap.current.get(menuKey);
        
        if (menuType && menuType.menuItem) {
          newSelectedKeys.push(menuKey);
        }
        if (menuType && menuType.subMenu && !openKeys.includes(menuKey)) {
          newOpenKeys.push(menuKey);
        }
      }
    });

    // 如果没有找到匹配的菜单项，尝试匹配动态路由的基础路径
    if (newSelectedKeys.length === 0) {
      const pathParts = pathname.split('/').filter(part => part);
      for (let i = pathParts.length; i > 0; i--) {
        const partialPath = pathParts.slice(0, i).join('/');
        const menuType = menuMap.current.get(partialPath);
        if (menuType && menuType.menuItem) {
          newSelectedKeys.push(partialPath);
          break;
        }
      }
      
      // 同样处理openKeys
      for (let i = pathParts.length; i > 0; i--) {
        const partialPath = pathParts.slice(0, i).join('/');
        const menuType = menuMap.current.get(partialPath);
        if (menuType && menuType.subMenu && !openKeys.includes(partialPath)) {
          newOpenKeys.push(partialPath);
        }
      }
    }

    setSelectedKeys(newSelectedKeys);
    setOpenKeys(newOpenKeys);
  }

  // 获取面包屑配置的函数，支持动态路由匹配
  const getBreadcrumbConfig = (currentPathname: string) => {
    // 首先尝试完全匹配
    let routeConfig = routeMap.current.get(currentPathname);
    if (routeConfig) {
      return routeConfig;
    }

    // 如果完全匹配失败，尝试匹配路径的前缀部分（支持动态路由）
    const pathParts = currentPathname.split('/').filter(part => part);
    
    for (let i = pathParts.length; i > 0; i--) {
      const partialPath = '/' + pathParts.slice(0, i).join('/');
      routeConfig = routeMap.current.get(partialPath);
      if (routeConfig) {
        return routeConfig;
      }
    }

    return [];
  };

  useEffect(() => {
    const routeConfig = getBreadcrumbConfig(pathname);
    setBreadCrumb(routeConfig || []);
    updateMenuStatus();

    // 设置菜单宽度CSS变量，供dropdown正确定位使用
    document.documentElement.style.setProperty('--menu-width', `${menuWidth}px`);
  }, [pathname, menuWidth]);

  // 当collapsed状态改变时更新菜单宽度变量
  useEffect(() => {
    document.documentElement.style.setProperty('--menu-width', `${menuWidth}px`);
  }, [collapsed, menuWidth]);

  // 来获取带图标的面包屑内容和路由返回
  function getBreadcrumbItem(node, index, length) {
    if (typeof node === 'string') {
      // 从路径中获取当前路由的key
      let currentKey = '';
      if (pathname.indexOf('/') === 0) {
        currentKey = pathname.substr(1);
      }
      if (currentKey) {
        // 检查是否为顶级菜单项（不包含'/'的路由key）
        let isTopLevelMenu = true;
        let iconDom = getIconFromKey(currentKey, false);
        if (!iconDom) {
          if (pathname.indexOf('/') > -1) {
            // 全路径没有再截取第一段路径去获取
            const paths = currentKey.split('/');
            iconDom = getIconFromKey(paths[0], false);
            if (!iconDom) {
              isTopLevelMenu = false;
            }
          } else {
            isTopLevelMenu = false;
          }
        }
        let menuname = '';
        if (breadcrumbMenuName) {
          // 对于最后一级面包屑，使用当前路径查找
          if (index + 1 === length) {
            // 首先尝试完全匹配
            menuname = breadcrumbMenuName.get(pathname);
            
            // 如果完全匹配失败，尝试匹配动态路由的基础路径
            if (!menuname) {
              const pathParts = pathname.split('/').filter(part => part);
              for (let i = pathParts.length; i > 0; i--) {
                const partialPath = '/' + pathParts.slice(0, i).join('/');
                const partialMenuname = breadcrumbMenuName.get(partialPath);
                if (partialMenuname) {
                  menuname = partialMenuname;
                  break;
                }
              }
            }
          } else {
            // 对于中间层级的面包屑，根据层级构建路径查找
            const pathParts = pathname.split('/').filter(part => part);
            const currentLevelPath = '/' + pathParts.slice(0, index + 1).join('/');
            menuname = breadcrumbMenuName.get(currentLevelPath);
          }
        }
        const route = findRouteByName(routes, node);
        return (
          <span
            className={styles['breadcrumb-item']}
            onClick={() => {
              // 使用 navigate 进行导航
              if (route) {
                NProgress.start();
                // 检查是否有preload方法（兼容性处理）
                if (route.component && typeof route.component.preload === 'function') {
                  route.component.preload().then(() => {
                    navigate("/" + route.key);
                    NProgress.done();
                  });
                } else {
                  // 直接导航，React.lazy会处理异步加载
                  navigate("/" + route.key);
                  NProgress.done();
                }
              }
            }}
            style={{ cursor: 'pointer' }}
          >
            {index == 0 && isTopLevelMenu && iconDom}
            <span className={styles['breadcrumb-text']}>
              {menuname || locale[node] || node}
            </span>
          </span>
        );
      }
    }
    return node;
  }

  const handleUserInfoClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发 Menu 的 onClickMenuItem
    setMenuVisible(!menuVisible); // 切换下拉菜单显示状态
  };

  // 添加useEffect处理全局点击事件，让SettingDropdownMenu能够处理自己的状态
  useEffect(() => {
    return () => {
      // 清理函数，当前无需执行特定操作
    };
  }, [menuVisible]);

  // 添加底部菜单渲染函数
  function renderBottomMenu() {
    return (
      <div className={styles['bottom-menu']} >
        {/* <div className={styles['collapse-btn']} onClick={toggleCollapse}>
          {collapsed ? <IconMenuUnfold /> : <IconMenuFold />}
        </div> */}
        <Menu
          collapse={collapsed}
          onClickMenuItem={onClickSubMenu}
          selectedKeys={selectedKeys}
        >
          <MenuItem key="enterprise" className="enterprise-menu-item">
            <div className={styles.enterprise}>
              <div className='flex items-center'>
                <IconEnterprise className={styles.icon} />
                {!collapsed && <span>{userInfo?.tenant_name || '智用开物'}</span>}
              </div>
              <span className={styles.enterprisetag}>管理</span>
            </div>
          </MenuItem>
          <div >
            <MenuItem key="user" onClick={handleUserInfoClick}>
              <div className={styles.userMenuContainer}>
                <div className={styles.userMenuLeft}>
                  {userInfo && (
                    <div className={styles.avatarIcon}>
                      <img
                        src={isValidAvatarUrl(userInfo.avatar_url || userInfo.avatar) ? (userInfo.avatar_url || userInfo.avatar) : DefaultAvatar}
                        alt="User Avatar"
                        className={styles.userAvatar}
                      />
                    </div>
                  )}
                  {!collapsed && (
                    <span className={styles.userName}>{userInfo?.full_name}</span>
                  )}
                </div>
                {!collapsed && <IconMore className={styles.moreIcon} />}
              </div>
            </MenuItem>
          </div>
        </Menu>
      </div>
    );
  }

  return (
    <Layout className={styles.layout}>
      {userLoading ? (
        <Spin className={styles['spin']} />
      ) : (
        <Layout>
          <Sider
            className={styles['layout-sider']}
            width={menuWidth}
            collapsed={collapsed}
            onCollapse={setCollapsed}
            trigger={null}
            collapsible
            breakpoint="xl"
            style={paddingTop}
          >
            <div className={styles['logo-box']}>
              <div className={styles.logo}>
                <Logo />
              </div>
              {!collapsed && (
                <span className={styles['logo-name']}>Agent Foundry</span>
              )}
            </div>
            <div className={styles['menu-wrapper']}>
              <Menu
                collapse={collapsed}
                onClickMenuItem={onClickMenuItem}
                selectedKeys={selectedKeys}
                openKeys={openKeys}
                onClickSubMenu={(key, openKeys) => {
                  setOpenKeys(openKeys);
                  onClickSubMenu(key);
                }}
              >
                {renderRoutes(locale)(routes, 1)}
              </Menu>
            </div>
            {renderBottomMenu()}
          </Sider>

          {/* 始终渲染SettingDropdownMenu，通过CSS控制显示/隐藏 */}
          {userInfo && (
            <SettingDropdownMenu
              userInfo={userInfo}
              menuVisible={menuVisible}
              setMenuVisible={setMenuVisible}
            />
          )}

          <Layout className={styles['layout-content']} style={paddingStyle}>
            <div className={styles['layout-content-wrapper']}>
              {!!breadcrumb.length && (
                <div className={styles['layout-breadcrumb']}>
                  <Breadcrumb separator={<IconRight />}>
                    {breadcrumb.map((node, index) => (
                      <Breadcrumb.Item key={index}>
                        {getBreadcrumbItem(node, index, breadcrumb.length)}
                      </Breadcrumb.Item>
                    ))}
                  </Breadcrumb>
                </div>
              )}
              <Content className="flex-1 flex flex-col">
{renderCurrentRoute()}
              </Content>
            </div>
            {/* {showFooter && <Footer />} */}
          </Layout>
        </Layout>
      )}
    </Layout>
  );
}

export default PageLayout;
