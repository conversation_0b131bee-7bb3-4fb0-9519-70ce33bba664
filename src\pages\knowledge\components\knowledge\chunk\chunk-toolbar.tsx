import FilterIcon from '../svg/filter.svg';
import { KnowledgeRouteKey } from '../constants/knowledge';
import { IChunkListResult, useSelectChunkList } from '../hooks/chunk-hooks';
import { useGetKnowledgeSearchParams } from '../hooks/route-hook';
import {
  LeftOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  DownOutlined,
  FilePdfOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Flex,
  Input,
  Menu,
  MenuProps,
  Popover,
  Radio,
  RadioChangeEvent,
  Segmented,
  SegmentedProps,
  Space,
  Typography,
  Select,
} from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

export enum ChunkTextMode {
  Full = 'full',
  Ellipse = 'ellipse',
}

const { Text } = Typography;

interface IProps
  extends Pick<
    IChunkListResult,
    'searchString' | 'handleInputChange' | 'available' | 'handleSetAvailable'
  > {
  checked: boolean;
  selectAllChunk: (checked: boolean) => void;
  createChunk: () => void;
  removeChunk: () => void;
  switchChunk: (available: number) => void;
  changeChunkTextMode(mode: ChunkTextMode): void;
  total: number;
  setSelectedChunkIds: string[];
  cancelBatchOperate: () => void;
  openBatchOperate: () => void;
  showBatchOperate: boolean;
}

const ChunkToolBar = ({
  selectAllChunk,
  checked,
  createChunk,
  removeChunk,
  switchChunk,
  changeChunkTextMode,
  available,
  handleSetAvailable,
  searchString,
  handleInputChange,
  total,
  setSelectedChunkIds,
  cancelBatchOperate,
  openBatchOperate,
  showBatchOperate,
}: IProps) => {
  const navigate = useNavigate();
  const data = useSelectChunkList();
  const documentInfo = data?.documentInfo;
  const { knowledgeId: knowledgeBaseId } = useGetKnowledgeSearchParams();
  const [isShowSearchBox, setIsShowSearchBox] = useState(false);

  const handleSelectAllCheck = useCallback(
    (e: any) => {
      selectAllChunk(e.target.checked);
    },
    [selectAllChunk]
  );

  const handleSearchIconClick = () => {
    setIsShowSearchBox(true);
  };

  const handleSearchBlur = () => {
    if (!searchString?.trim()) {
      setIsShowSearchBox(false);
    }
  };

  const handleDelete = useCallback(() => {
    removeChunk();
  }, [removeChunk]);

  const handleEnabledClick = useCallback(() => {
    switchChunk(1);
  }, [switchChunk]);

  const handleDisabledClick = useCallback(() => {
    switchChunk(0);
  }, [switchChunk]);

  const items: MenuProps['items'] = useMemo(() => {
    return [
      {
        key: '1',
        label: (
          <>
            <Checkbox onChange={handleSelectAllCheck} checked={checked}>
              <b>{'选择所有'}</b>
            </Checkbox>
          </>
        ),
      },
      { type: 'divider' },
      {
        key: '2',
        label: (
          <Space onClick={handleEnabledClick}>
            <CheckCircleOutlined />
            <b>{'启用选定的'}</b>
          </Space>
        ),
      },
      {
        key: '3',
        label: (
          <Space onClick={handleDisabledClick}>
            <CloseCircleOutlined />
            <b>{'禁用选定的'}</b>
          </Space>
        ),
      },
      { type: 'divider' },
      {
        key: '4',
        label: (
          <Space onClick={handleDelete}>
            <DeleteOutlined />
            <b>{'删除选定的'}</b>
          </Space>
        ),
      },
    ];
  }, [
    checked,
    handleSelectAllCheck,
    handleDelete,
    handleEnabledClick,
    handleDisabledClick,
  ]);

  const content = (
    <Menu style={{ width: 200 }} items={items} selectable={false} />
  );

  const handleFilterChange = (e: RadioChangeEvent) => {
    selectAllChunk(false);
    handleSetAvailable(e.target.value);
  };

  const handleSortChange = () => {};

  const filterContent = (
    <Radio.Group onChange={handleFilterChange} value={available}>
      <Space direction="vertical">
        <Radio value={undefined}>{'所有'}</Radio>
        <Radio value={1}>{'启用'}</Radio>
        <Radio value={0}>{'禁用'}</Radio>
      </Space>
    </Radio.Group>
  );

  const filterRender = useMemo(() => {
    if (showBatchOperate) {
      return (
        <>
          <Button
            color="default"
            variant="filled"
            style={{ cursor: 'default' }}
          >
            已选择 {setSelectedChunkIds.length} 项
          </Button>
          <Button onClick={handleDelete} color="danger" variant="outlined">
            删除
          </Button>
          <Button onClick={cancelBatchOperate}>取消</Button>
        </>
      );
    } else {
      return (
        <>
          <span className="text-[#adadad]">共 {total} 个分段</span>
          {/* <Segmented
          options={[
            { label: '全文', value: ChunkTextMode.Full },
            { label: '省略', value: ChunkTextMode.Ellipse },
          ]}
          onChange={changeChunkTextMode as SegmentedProps['onChange']}
        /> */}

          <Select
            className="min-w-[160px]"
            defaultValue="1"
            style={{ width: 120 }}
            onChange={handleSortChange}
            options={[{ value: '1', label: '按创建时间排序' }]}
          />
          {/* <Popover content={content} placement="bottom" arrow={false}>
          <Button>
            {'批量'}
            <DownOutlined />
          </Button>
        </Popover> */}
          {/* {isShowSearchBox ? (
          <Input
            size="middle"
            placeholder={'搜索'}
            prefix={<SearchOutlined />}
            allowClear
            onChange={handleInputChange}
            onBlur={handleSearchBlur}
            value={searchString}
          />
        ) : (
          <Button icon={<SearchOutlined />} onClick={handleSearchIconClick} />
        )} */}

          <Input
            className="min-w-[240px]"
            size="middle"
            placeholder={'AI搜索...'}
            prefix={<SearchOutlined />}
            allowClear
            onChange={handleInputChange}
            value={searchString}
          />
          <Button onClick={openBatchOperate}>批量操作</Button>
          {/* <Popover content={filterContent} placement="bottom" arrow={false}>
          <Button icon={<FilterIcon />} />
        </Popover> */}
          {/* <Button
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => createChunk()}
        /> */}
        </>
      );
    }
  }, [total, showBatchOperate, setSelectedChunkIds]);

  return (
    <Flex justify="space-between" align="center">
      <Space size={'middle'}>
        <a
          className="not-hover:!text-inherit"
          onClick={(e) => {
            e.stopPropagation();
            navigate(
              `/knowledge/${KnowledgeRouteKey.Dataset}?id=${knowledgeBaseId}`
            );
          }}
        >
          <LeftOutlined />
        </a>
        <FilePdfOutlined />
        <Text ellipsis={{ tooltip: documentInfo?.name }} style={{ width: 150 }}>
          {documentInfo?.name}
        </Text>
      </Space>
      <Space>{filterRender}</Space>
    </Flex>
  );
};

export default ChunkToolBar;
