import { useFetchFileList } from '@/pages/knowledge/components/file-manager/src/hooks/file-manager-hooks';
import { IFile } from '@/pages/knowledge/components/file-manager/src/interfaces/database/file-manager';
import { formatDate } from '@/pages/knowledge/components/file-manager/src/utils/date';
import { Button, Flex, Space, Table, Tag, Typography } from 'antd';
import { useState } from 'react';
import { ColumnsType } from 'antd/es/table';
import ActionCell from './action-cell';
import FileToolbar from './file-toolbar';
import {
  useGetRowSelection,
  useHandleConnectToKnowledge,
  useHandleCreateFolder,
  useHandleMoveFile,
  useHandleUploadFile,
  useNavigateToOtherFolder,
  useEditCurrentFile,
  useGetFolderId,
  useHandleCardDelete,
} from './hooks';

import FileUploadModal from '@/pages/knowledge/components/file-manager/src/components/file-upload-modal';
import RenameModal from '@/pages/knowledge/components/file-manager/src/components/rename-modal';
import SvgIcon from '@/pages/knowledge/components/file-manager/src/components/svg-icon';
import { useTranslate } from '@/pages/knowledge/components/file-manager/src/hooks/common-hooks';
import { formatNumberWithThousandsSeparator } from '@/pages/knowledge/components/file-manager/src/utils/common-util';
import { getExtension } from '@/pages/knowledge/components/file-manager/src/utils/document-util';
import ConnectToKnowledgeModal from './connect-to-knowledge-modal';
import FolderCreateModal from './folder-create-modal';
import styles from './index.module.less';
import FileMovingModal from './move-file-modal';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import FileCardList from './file-card-list';


const { Text } = Typography;

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 10, // 10 minutes (React Query v4 用 cacheTime)
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

const FileManager = () => {
  const { t } = useTranslate('fileManager');
  const { rowSelection, setSelectedRowKeys } = useGetRowSelection();
  const [isBatchMode, setIsBatchMode] = useState(false);
  const currentFolderId = useGetFolderId();

  // 判断是否为根目录（主页）
  const isRootDirectory = !currentFolderId;

  // 批量操作相关函数
  const handleEnterBatchMode = () => {
    setIsBatchMode(true);
    setSelectedRowKeys([]);
  };

  const handleExitBatchMode = () => {
    setIsBatchMode(false);
    setSelectedRowKeys([]);
  };

  const navigateToOtherFolder = useNavigateToOtherFolder();
  const {
    fileEditVisible,
    fileEditLoading,
    hideFileEditModal,
    showFileEditModal,
    onFileEditOk,
    initialName,
    initialDescription,
    initialTags,
  } = useEditCurrentFile();
  const {
    folderCreateModalVisible,
    showFolderCreateModal,
    hideFolderCreateModal,
    folderCreateLoading,
    onFolderCreateOk,
  } = useHandleCreateFolder();
  const {
    fileUploadVisible,
    hideFileUploadModal,
    showFileUploadModal,
    fileUploadLoading,
    onFileUploadOk,
  } = useHandleUploadFile();
  const {
    connectToKnowledgeVisible,
    hideConnectToKnowledgeModal,
    showConnectToKnowledgeModal,
    onConnectToKnowledgeOk,
    initialValue,
    connectToKnowledgeLoading,
  } = useHandleConnectToKnowledge();
  const {
    showMoveFileModal,
    moveFileVisible,
    onMoveFileOk,
    hideMoveFileModal,
    moveFileLoading,
  } = useHandleMoveFile(setSelectedRowKeys);
  const { pagination, data, searchString, handleInputChange, loading } =
    useFetchFileList();
  const { handleCardDelete } = useHandleCardDelete();


  // 处理卡片重命名
  const handleCardRename = (file: IFile) => {
    showFileEditModal(file);
  };

  // 表格列配置
  const columns: ColumnsType<IFile> = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      // fixed: 'left',
      render: (_, __, index) => {
        // 计算实际序号（考虑分页）
        const currentPage = pagination?.current || 1;
        const pageSize = pagination?.pageSize || 10;
        const actualIndex = (currentPage - 1) * pageSize + index + 1;
        return (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'left',
            width: '100%',
            height: '100%'
          }}>
            {actualIndex}
          </div>
        );
      },
    },
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      render(value, record) {
        return (
          <Flex gap={10} align="center">
            <SvgIcon
              name={`file-icon/${record.type === 'folder' ? 'folder' : getExtension(value)}`}
              width={24}
            ></SvgIcon>
            {record.type === 'folder' ? (
              <Button
                type={'link'}
                className={styles.linkButton}
                onClick={() => navigateToOtherFolder(record.id)}
              >
                <Text ellipsis={{ tooltip: value }}>{value}</Text>
              </Button>
            ) : (
              <Text ellipsis={{ tooltip: value }}>{value}</Text>
            )}
          </Flex>
        );
      },
    },
    {
      title: t('type'),
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render(value, record) {
        if (record.type === 'folder') {
          // 如果是文件夹，显示文件夹标识
          return (
            <span className={styles.fileTypeTag}>
              文件夹
            </span>
          );
        } else {
          // 如果是文件，显示文件扩展名
          const extension = getExtension(record.name);
          const fileType = extension ? extension.toLowerCase() : '文件';
          return (
            <span className={styles.fileTypeTag}>
              {fileType}
            </span>
          );
        }
      },
    },
    {
      title: t('size'),
      dataIndex: 'size',
      key: 'size',
      render(value) {
        return (
          formatNumberWithThousandsSeparator((value / 1024).toFixed(2)) + ' KB'
        );
      },
    },
    {
      title: t('uploadDate'),
      dataIndex: 'create_time',
      key: 'create_time',
      render(text) {
        return formatDate(text);
      },
    },
    // {
    //   title: t('knowledgeBase'),
    //   dataIndex: 'kbs_info',
    //   key: 'kbs_info',
    //   render(value) {
    //     return Array.isArray(value) ? (
    //       <Space wrap>
    //         {value?.map((x) => (
    //           <Tag color="blue" key={x.kb_id}>
    //             {x.kb_name}
    //           </Tag>
    //         ))}
    //       </Space>
    //     ) : (
    //       ''
    //     );
    //   },
    // },
    {
      title: t('action'),
      dataIndex: 'action',
      key: 'action',
      render: (text, record) => (
        <ActionCell
          record={record}
          setCurrentRecord={(record: any) => {
            console.info(record);
          }}
          showRenameModal={showFileEditModal}
          showMoveFileModal={showMoveFileModal}
          showConnectToKnowledgeModal={showConnectToKnowledgeModal}
          setSelectedRowKeys={setSelectedRowKeys}
        ></ActionCell>
      ),
    },
  ];

  // 渲染卡片视图（主页）
  const renderCardView = () => {
    if (!data?.files) return null;

    return (
      <FileCardList
        files={data.files}
        onNavigate={navigateToOtherFolder}
        onRename={handleCardRename}
        onDelete={handleCardDelete}
      />
    );
  };

  // 渲染表格视图（二级文件夹）
  const renderTableView = () => {
    return (
      <Table
        dataSource={data?.files}
        columns={columns}
        rowKey={'id'}
        rowSelection={isBatchMode ? rowSelection : undefined}
        loading={loading}
        pagination={pagination}
        scroll={{ scrollToFirstRowOnChange: true, x: '100%' }}
        showHeader={false} //隐藏表格头部
        className={styles.fileTable}
      />
    );
  };

  return (
    <section className={styles.fileManagerWrapper}>
      <FileToolbar
        searchString={searchString}
        handleInputChange={handleInputChange}
        selectedRowKeys={rowSelection.selectedRowKeys as string[]}
        showFolderCreateModal={showFolderCreateModal}
        showFileUploadModal={showFileUploadModal}
        setSelectedRowKeys={setSelectedRowKeys}
        showMoveFileModal={showMoveFileModal}
        total={data?.total || 0}
        isBatchMode={isBatchMode}
        onEnterBatchMode={handleEnterBatchMode}
        onExitBatchMode={handleExitBatchMode}
        dataSource={data?.files || []}
      ></FileToolbar>

      {/* 根据当前路径决定显示卡片还是表格 */}
      {isRootDirectory ? renderCardView() : renderTableView()}

      <RenameModal
        visible={fileEditVisible}
        hideModal={hideFileEditModal}
        onOk={onFileEditOk}
        loading={fileEditLoading}
        initialName={initialName}
        initialDescription={initialDescription}
        initialTags={initialTags}
      ></RenameModal>
      <FolderCreateModal
        loading={folderCreateLoading}
        visible={folderCreateModalVisible}
        hideModal={hideFolderCreateModal}
        onOk={onFolderCreateOk}
      ></FolderCreateModal>
      <FileUploadModal
        visible={fileUploadVisible}
        hideModal={hideFileUploadModal}
        loading={fileUploadLoading}
        onOk={onFileUploadOk}
      ></FileUploadModal>
      <ConnectToKnowledgeModal
        initialValue={initialValue}
        visible={connectToKnowledgeVisible}
        hideModal={hideConnectToKnowledgeModal}
        onOk={onConnectToKnowledgeOk}
        loading={connectToKnowledgeLoading}
      ></ConnectToKnowledgeModal>
      {moveFileVisible && (
        <FileMovingModal
          visible={moveFileVisible}
          hideModal={hideMoveFileModal}
          onOk={onMoveFileOk}
          loading={moveFileLoading}
        ></FileMovingModal>
      )}
    </section>
  );
};

// 使用 QueryClientProvider 包装组件以提供 React Query 上下文
const FileManagerWithQueryProvider = () => {
  return (
    // <QueryClientProvider client={queryClient}>
      <FileManager />
    // </QueryClientProvider>
  );
};

export default FileManagerWithQueryProvider;
