worker_processes  1;
error_log  /usr/local/etc/nginx/logs/error.log  debug;

events {
    worker_connections  1024;
}

http {
    resolver ***************;
    include       mime.types;
    default_type  application/octet-stream;
    client_max_body_size 100m;
    charset utf-8;
    
    # 确保正确处理UTF-8编码的URL
    merge_slashes off;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$request_uri" "$proxy_host"'
                      '$upstream_http_status $upstream_response_time';

    proxy_headers_hash_max_size 1024;
    proxy_headers_hash_bucket_size 128;

    sendfile        on;
    keepalive_timeout  65;

    map $http_x_grpc_web $use_grpc_location {
        default 0;
        "~." 1;
    }

    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    server {
        listen 80;
        server_name _;

        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location ~ ^/api/(.*)$ {
            # 添加调试响应头
            add_header 'X-Debug-Original-URI' '$request_uri' always;
            add_header 'X-Debug-Proxy-To' '${API_BASE_URL}/$1' always;
            add_header 'X-Debug-Args' '$1' always;

            # 处理URL编码，保持中文字符正确传递
            set $backend_url $1;
            set $args $args;
            
            proxy_pass ${API_BASE_URL}/$backend_url$is_args$args;
            
            proxy_set_header Host ${API_HOST};
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Authorization $http_authorization;
            proxy_set_header User-Agent $http_user_agent;
            
            # WebSocket支持
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_http_version 1.1;
            
            # 增加WebSocket超时时间
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
            
            # SSL设置 - 修复SNI域名匹配问题
            proxy_ssl_verify off;
            proxy_ssl_server_name on;
            proxy_ssl_name ${API_HOST};
            
            # 确保正确处理UTF-8编码
            proxy_set_header Accept-Charset "utf-8";
            
            # CORS配置
            add_header 'Access-Control-Allow-Origin' 'http://localhost:3000' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, Accept-Charset' always;
            
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Max-Age' 1728000;
                return 204;
            }
        }

        # 全局代理配置 - 使用环境变量
        # location /elsa {
        #     proxy_pass ${ELSA_BASE_URL}/elsa;
        #     proxy_set_header Authorization $http_authorization;
        # }

        # location /_content {
        #     proxy_pass ${ELSA_BASE_URL}/_content;
        #     proxy_set_header Authorization $http_authorization;
        # }

        # location /_framework {
        #     proxy_pass ${ELSA_BASE_URL}/_framework;
        #     proxy_set_header Authorization $http_authorization;
        # }

        # location /workflows {
        #     proxy_pass ${ELSA_BASE_URL}/workflows;
        #     proxy_set_header Authorization $http_authorization;
        # }

        # 精准拦截CSS请求（保持不变）
        location = /_content/MudBlazor/MudBlazor.min.css {
            alias /usr/local/etc/nginx/custom_styles/mudblazor.min.css; # 网页7路径规范
            add_header Content-Type "text/css";
            access_log off;
            # expires 7d;
            add_header Expires "0";
        }

        location /_content/Elsa.Studio.Shell/css/shell.css {
            alias /usr/local/etc/nginx/custom_styles/shell.css;
            add_header Content-Type "text/css";
            access_log off;
            # expires 7d;
            add_header Expires "0";
        }

        location /_content/Radzen.Blazor/css/material-base.css {
            alias /usr/local/etc/nginx/custom_styles/material-base.css;
            add_header Content-Type "text/css";
            access_log off;
            # expires 7d;
            add_header Expires "0";
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }

    include servers/*;
}