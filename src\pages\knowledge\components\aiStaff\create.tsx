import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Message } from '@arco-design/web-react';
import { useNavigate } from 'react-router-dom';
import AiStaffBasic from './components/basic';
import AiStaffSettings from './components/settings';
import styles from './style/index.module.less';
import { createAgent, getAgentDetail } from '@/lib/services/agent-service';
import { createEmployee } from '@/pages/knowledge/components/knowledge/services/aiStaff-service';

const TabPane = Tabs.TabPane;

interface KnowledgeBaseItem {
  name: string;
  type: string;
  disabled?: boolean;
}

interface AiStaffFormData {
  name: string;
  description: string;
  abilityAgentId?: string;
  abilityName?: string;
  prompt?: string;
  knowledges?: KnowledgeBaseItem[];
  tags?: string[];
  permission?: string;
  // ... 其他AI员工专属字段
}

// 主流程同款智能体参数模板
const defaultAgentParams: any = {
  name: '',
  description: '',
  type: 'task',
  instruction: '',
  templates: [],
  functions: [],
  responses: [],
  samples: [],
  utilities: [],
  knowledge_bases: [],
  rules: [],
  routing_rules: [],
  disabled: false,
  icon_url: null,
  profiles: [],
  llm_config: {
    is_inherit: false,
    provider: 'azure-openai',
    model: 'gpt-4o-mini',
    max_recursion_depth: 3,
    max_tokens: null,
    temperature: null,
  },
  applicationId: '',
  labels: [],
  ts_cards: [],
  acp_tools: [],
};

export default function AiStaffCreate() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<AiStaffFormData>({});
  const [activeTab, setActiveTab] = useState('basic');
  const [isEditing] = useState(true);

  // 取消按钮
  const handleCancel = () => {
    navigate('/knowledge/aiStaff');
  };

  // 保存按钮（预留实现）
  const handleSave = useCallback(async () => {
    setLoading(true);
    try {
      // 1. 构造新agent参数，优先用表单数据覆盖
      const agentParams = {
        ...defaultAgentParams,
        name: formData.abilityName || '',
        description: formData.description || '',
        instruction: formData.prompt || '',
        knowledge_bases: (formData.knowledges || []).map((kb) => ({
          name: kb.name,
          disabled: kb.disabled,
          type: kb.type,
        })),
        labels: formData.tags || [],
        is_public: formData.permission === 'public',
      };
      // 2. 创建新agent
      const agentRes = await createAgent(agentParams);
      const agentId = agentRes?.agent_id;
      if (!agentId) throw new Error('智能体创建失败');
      // 3. 创建AI员工
      await createEmployee({
        name: formData.name,
        description: formData.description,
        tags: formData.tags,
        isPublic: formData.permission === 'public',
        agentId,
      });
      Message.success('创建成功');
      navigate('/knowledge/aiStaff');
    } catch (e) {
      Message.error('创建失败');
    } finally {
      setLoading(false);
    }
  }, [formData, navigate]);

  if (loading) {
    return (
      <div
        className={styles.container}
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px',
        }}
      >
        <Spin dot size={40} />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Tabs
        activeTab={activeTab}
        onChange={setActiveTab}
        className={styles.tabs}
      >
        <TabPane key="basic" title="基础信息">
          <AiStaffBasic
            formData={formData}
            setFormData={setFormData}
            isEditing={isEditing}
          />
        </TabPane>
        <TabPane key="settings" title="配置">
          <AiStaffSettings
            formData={formData}
            setFormData={setFormData}
            isEditing={isEditing}
          />
        </TabPane>
      </Tabs>
      <div
        style={{
          marginTop: 32,
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 16,
        }}
      >
        <Button type="secondary" onClick={handleCancel}>
          取消
        </Button>
        <Button type="primary" onClick={handleSave} disabled={!formData.name}>
          创建
        </Button>
      </div>
    </div>
  );
}
