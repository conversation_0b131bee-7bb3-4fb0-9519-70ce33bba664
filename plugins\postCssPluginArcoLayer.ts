import postcss from 'postcss';
import type { Plugin } from 'postcss';

/**
 * 这是我们的核心：一个自定义的 PostCSS 插件。
 * @param {object} opts - 插件选项，我们让 layer 的名字可以自定义。
 * @returns {import('postcss').Plugin}
 */
export const postCssPluginArcoLayer = (
  opts: {
    layer?: 'components' | 'utilities' | 'base';
  } = {}
): Plugin => {
  const layerName = opts.layer || 'components'; // 默认层级是 'components'
  const ARCO_DESIGN_PACKAGE = '@arco-design/web-react';

  return {
    postcssPlugin: 'postcss-plugin-arco-layer', // 插件名称
    // 使用 Root 钩子，它会在 postcss 处理每个 CSS 文件时执行
    Root(root) {
      const filePath = root.source?.input.file;

      // 关键判断：只处理来自 @arco-design/web-react 包内部的样式文件
      if (filePath && filePath.includes(ARCO_DESIGN_PACKAGE)) {
        // 创建一个新的 @layer 规则，例如 @layer components
        const layerRule = postcss.atRule({ name: 'layer', params: layerName });

        // 将当前 CSS 文件中的所有规则（root.nodes）都移动到新的 layer 规则内部
        layerRule.append(root.nodes);

        // 清空 root，并将我们新创建的、包含了所有旧规则的 layer 规则添加回去
        root.append(layerRule);
      }
    },
  };
};
