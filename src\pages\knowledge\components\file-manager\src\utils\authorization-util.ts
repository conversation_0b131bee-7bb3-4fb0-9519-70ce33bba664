import { Authorization, Token, UserInfo } from '@/pages/knowledge/components/file-manager/src/constants/authorization';
import { getSearchValue } from './common-util';
import { getUserStore } from '@/lib/helpers/store';

const KeycloakToken = 'KeycloakToken';
const KeySet = [Authorization, Token, UserInfo, KeycloakToken];

const storage = {
  getAuthorization: () => {
    return localStorage.getItem(Authorization);
  },
  getToken: () => {
    return localStorage.getItem(Token);
  },
  getUserInfo: () => {
    return localStorage.getItem(UserInfo);
  },
  getUserInfoObject: () => {
    return JSON.parse(localStorage.getItem('userInfo') || '');
  },
  setAuthorization: (value: string) => {
    localStorage.setItem(Authorization, value);
  },
  setToken: (value: string) => {
    localStorage.setItem(Token, value);
  },
  setUserInfo: (value: string | Record<string, unknown>) => {
    const valueStr = typeof value !== 'string' ? JSON.stringify(value) : value;
    localStorage.setItem(UserInfo, valueStr);
  },
  setItems: (pairs: Record<string, string>) => {
    Object.entries(pairs).forEach(([key, value]) => {
      localStorage.setItem(key, value);
    });
  },
  removeAuthorization: () => {
    localStorage.removeItem(Authorization);
  },
  removeAll: () => {
    KeySet.forEach((x) => {
      localStorage.removeItem(x);
    });
  },
  setLanguage: (lng: string) => {
    localStorage.setItem('lng', lng);
  },
  getLanguage: (): string => {
    return localStorage.getItem('lng') as string;
  },
  setKeycloakToken: (token: string) => {
    localStorage.setItem(KeycloakToken, token);
  },
  getKeycloakToken: () => {
    return localStorage.getItem(KeycloakToken);
  },
  removeKeycloakToken: () => {
    localStorage.removeItem(KeycloakToken);
  },
};

export const getAuthorization = () => {
  // 优先使用 getUserStore 获取的 token
  try {
    const mainProjectUser = getUserStore();
    if (mainProjectUser && mainProjectUser.token) {
      console.log('🔑 使用 getUserStore 获取的 token');
      return `Bearer ${mainProjectUser.token}`;
    }
  } catch (error) {
    console.warn('获取 getUserStore token 失败:', error);
  }

  // 开发环境：使用固定的JWT token（作为备用）
  if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {
    const fixedToken = '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    console.log('🔑 开发环境：使用固定的JWT token（备用）');
    return `Bearer ${fixedToken}`;
  }

  // 然后检查Keycloak token
  const keycloakToken = storage.getKeycloakToken();
  if (keycloakToken) {
    return `Bearer ${keycloakToken}`;
  }

  // 然后检查URL参数中的auth
  const auth = getSearchValue('auth');
  if (auth) {
    return `Bearer ${auth}`;
  }

  // 检查存储的Authorization
  const storedAuth = storage.getAuthorization();
  if (storedAuth) {
    return storedAuth;
  }

  return '';
};

// 设置Keycloak认证信息
export const setKeycloakAuth = (token: string, userInfo?: any) => {
  storage.setKeycloakToken(token);
  if (userInfo) {
    storage.setUserInfo(userInfo);
  }
};

// 新增：设置开发环境token的便捷方法
export const setDevToken = (token: string) => {
  localStorage.setItem('devToken', token);
  console.log('🔑 已设置开发环境token');
};

// 新增：获取当前使用的token信息
export const getTokenInfo = () => {
  try {
    const mainProjectUser = getUserStore();
    return {
      hasMainProjectToken: !!(mainProjectUser && mainProjectUser.token),
      hasKeycloakToken: !!storage.getKeycloakToken(),
      hasDevToken: !!localStorage.getItem('devToken'),
      hasUrlAuth: !!getSearchValue('auth'),
      hasStoredAuth: !!storage.getAuthorization(),
    };
  } catch (error) {
    return {
      hasMainProjectToken: false,
      hasKeycloakToken: !!storage.getKeycloakToken(),
      hasDevToken: !!localStorage.getItem('devToken'),
      hasUrlAuth: !!getSearchValue('auth'),
      hasStoredAuth: !!storage.getAuthorization(),
      error: error.message,
    };
  }
};

export default storage;

// Will not jump to the login page
export function redirectToLogin() {
  window.location.href = location.origin + `/login`;
}
