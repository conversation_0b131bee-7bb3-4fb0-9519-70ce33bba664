import { IDocumentInfo } from '../interfaces/document';
import { DocumentType, RunningStatus } from '../constants/knowledge';
import {
  Badge,
  DescriptionsProps,
  Flex,
  Popconfirm,
  Popover,
  Space,
  Tag,
  Tooltip,
} from 'antd';
import reactStringReplace from 'react-string-replace';
import RunIcon from '../svg/run.svg';
import CancelIcon from '../svg/cancel.svg';
import RefreshIcon from '../svg/refresh.svg';
import { useHandleRunDocumentByIds } from './hooks';
import { isParserRunning } from './utils';
import AnalysisSVG from '../svg/analysis.svg';

interface IProps {
  record: IDocumentInfo;
}

const RunningStatusMap = {
  [RunningStatus.UNSTART]: {
    label: '待处理',
    color: 'orange',
  },
  [RunningStatus.RUNNING]: {
    label: '解析中',
    color: 'blue',
  },
  [RunningStatus.CANCEL]: { label: '取消', color: 'orange' },
  [RunningStatus.DONE]: { label: '已解析', color: 'green' },
  [RunningStatus.FAIL]: { label: '失败', color: 'red' },
};

export const iconMap = {
  [RunningStatus.UNSTART]: AnalysisSVG,
  [RunningStatus.RUNNING]: CancelIcon,
  // [RunningStatus.CANCEL]: RefreshIcon,
  // [RunningStatus.DONE]: RefreshIcon,
  // [RunningStatus.FAIL]: RefreshIcon,
};

export const iconTipTextMap = {
  [RunningStatus.UNSTART]: '解析',
  [RunningStatus.RUNNING]: '取消',
  [RunningStatus.CANCEL]: '刷新',
  [RunningStatus.DONE]: '刷新',
  [RunningStatus.FAIL]: '刷新',
};

const PopoverContent = ({ record }: IProps) => {
  const replaceText = (text: string) => {
    // Remove duplicate \n
    const nextText = text.replace(/(\n)\1+/g, '$1');

    const replacedText = reactStringReplace(
      nextText,
      /(\[ERROR\].+\s)/g,
      (match, i) => {
        return (
          <span key={i} className="text-[red]">
            {match}
          </span>
        );
      }
    );

    return replacedText;
  };

  const items: DescriptionsProps['items'] = [
    {
      key: 'process_begin_at',
      label: '开始于',
      children: record.process_begin_at,
    },
    {
      key: 'process_duation',
      label: '持续时间',
      children: `${record.process_duation.toFixed(2)} s`,
    },
    {
      key: 'progress_msg',
      label: '进度',
      children: replaceText(record.progress_msg.trim()),
    },
  ];

  return (
    <Flex vertical className="w-[40vw]">
      {items.map((x, idx) => {
        return (
          <div key={x.key} className={idx < 2 ? 'flex gap-[10px]' : ''}>
            <b>{x.label}:</b>
            <div className="whitespace-pre-line max-h-[50vh] overflow-auto">
              {x.children}
            </div>
          </div>
        );
      })}
    </Flex>
  );
};

const ParsingStatusCell = ({ record }: IProps) => {
  const text = record.run;
  const runningStatus = RunningStatusMap[text];
  const label = runningStatus.label;

  const isRunning = isParserRunning(text);
  const OperationIcon = iconMap[text];
  const OperationIconTipText = iconTipTextMap[text];

  const { handleRunDocumentByIds } = useHandleRunDocumentByIds(record.id);

  const handleOperationIconClick =
    (shouldDelete = false) =>
    () => {
      handleRunDocumentByIds(record.id, isRunning, shouldDelete);
    };

  return record.type === DocumentType.Virtual ? null : (
    <Flex justify={'space-between'} align="center">
      <Popover content={<PopoverContent record={record}></PopoverContent>}>
        <Tag color={runningStatus.color}>
          {isRunning ? (
            <Space>
              <Badge color={runningStatus.color} />
              {label}
              <span>{(record.progress * 100).toFixed(2)}%</span>
            </Space>
          ) : (
            label
          )}
        </Tag>
      </Popover>
      {/* <Popconfirm
        title={`是否清空已有 ${record.chunk_num}个 chunk？`}
        onConfirm={handleOperationIconClick(true)}
        onCancel={handleOperationIconClick(false)}
        disabled={record.chunk_num === 0}
        okText={'是'}
        cancelText={'否'}
      >
        <div
          className={'text-center flex hover:cursor-pointer'}
          onClick={
            record.chunk_num === 0 ? handleOperationIconClick(false) : () => {}
          }
        >
          {OperationIcon && (
            <Tooltip title={OperationIconTipText}>{<OperationIcon />}</Tooltip>
          )}
        </div>
      </Popconfirm> */}
    </Flex>
  );
};

export default ParsingStatusCell;
