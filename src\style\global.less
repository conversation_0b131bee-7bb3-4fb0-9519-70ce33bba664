@import 'nprogress/nprogress.css';

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  background-color: var(--color-bg-1);
}

.chart-wrapper {
  .bizcharts-tooltip {
    background: linear-gradient(304.17deg,
        rgb(253 254 255 / 60%) -6.04%,
        rgb(244 247 252 / 60%) 85.2%) !important;
    border-radius: 6px;
    backdrop-filter: blur(10px);
    padding: 8px !important;
    width: 180px !important;
    opacity: 1 !important;
  }
}

body[arco-theme='dark'] {
  .chart-wrapper {
    .bizcharts-tooltip {
      background: linear-gradient(304.17deg,
          rgba(90, 92, 95, 0.6) -6.04%,
          rgba(87, 87, 87, 0.6) 85.2%) !important;
      backdrop-filter: blur(10px);
      border-radius: 6px;
      box-shadow: none !important;
    }
  }
}

.arco-menu-inline-header {
  line-height: 48px !important;
}

:not(.arco-layout-sider-collapsed) {
  .custom-menu-div {
    width: 87%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #a6a6a6;

    &.selected {
      font-weight: 600 !important;
      color: #333333;
    }
  }
}

.arco-layout-sider-collapsed {
  .custom-menu-div {
    width: auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// .custom-menu-div-collapsed {
//   width: 100%;
//   display: flex;
//   justify-content: center;
//   align-items: center;

//   &.selected {
//     font-weight: 600 !important;
//   }
// }

.arco-menu-inline-header {
  .arco-menu-icon-suffix {
    padding: 6px 6px;
    border-radius: 4px;
  }

  &:hover {
    .arco-menu-icon-suffix {
      &:hover {
        background-color: rgba(87, 87, 87, 0.06);

        svg {
          color: #000000 !important;
        }
      }
    }
  }
}

.arco-message-wrapper {
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  flex-direction: column !important;

  .arco-message {
    border-radius: 16px !important;
    height: 26px !important;
    display: flex !important;
    align-items: center !important;
  }
}