import { LlmModelType } from '../constants/knowledge';
import { useSelectLlmOptionsByModelType } from '../hooks/llm-hooks';
import { Form, Select } from 'antd';
import { useMemo } from 'react';

const enum DocumentType {
  DeepDOC = 'DeepDOC',
  PlainText = 'Plain Text',
}

const LayoutRecognize = () => {
  const allOptions = useSelectLlmOptionsByModelType();

  const options = useMemo(() => {
    const list = [DocumentType.DeepDOC, DocumentType.PlainText].map((x) => ({
      label: x === DocumentType.PlainText ? 'Naive' : 'DeepDoc',
      value: x,
    }));

    const image2TextList = allOptions[LlmModelType.Image2text].map((x) => {
      return {
        ...x,
        options: x.options.map((y) => {
          return {
            ...y,
            label: (
              <div className="flex justify-between items-center gap-2">
                {y.label}
                <span className="text-red-500 text-sm">Experimental</span>
              </div>
            ),
          };
        }),
      };
    });

    return [...list, ...image2TextList];
  }, [allOptions]);

  return (
    <Form.Item
      name={['parser_config', 'layout_recognize']}
      label={'PDF解析器'}
      initialValue={DocumentType.DeepDOC}
      tooltip={'使用视觉模型进行 PDF 布局分析，以更好地识别文档结构，找到标题、文本块、图像和表格的位置。 如果选择 Naive 选项，则只能获取 PDF 的纯文本。请注意该功能只适用于 PDF 文档，对其他文档不生效。欲了解更多信息，请参阅 https://ragflow.io/docs/dev/select_pdf_parser。'}
    >
      <Select options={options} popupMatchSelectWidth={false} />
    </Form.Item>
  );
};

export default LayoutRecognize;
