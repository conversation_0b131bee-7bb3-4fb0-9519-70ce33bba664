import React, { useEffect, useState, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Message, Spin, Button } from '@arco-design/web-react';
import AiStaffBasic from './components/basic';
import AiStaffSettings from './components/settings';
import {
  getEmployeeDetail,
  updateEmployee,
} from '@/pages/knowledge/components/knowledge/services/aiStaff-service';
import { getAgentDetail, updateAgent } from '@/lib/services/agent-service';
import styles from './style/index.module.less';

const defaultFormData = {
  name: '',
  description: '',
  tags: [],
  permission: 'public',
  knowledges: [],
  abilityAgentId: '',
  abilityName: '',
  prompt: '',
};

export default function AiStaffEdit() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [formData, setFormData] = useState(defaultFormData);
  const [loading, setLoading] = useState(true);
  const [isEditing] = useState(true);
  const [agentDetail, setAgentDetail] = useState<any>({}); // 新增

  // 拉取详情并填充表单
  useEffect(() => {
    if (!id) return;
    setLoading(true);
    getEmployeeDetail(id)
      .then(async (res) => {
        const data = res?.data || {};
        let agent = {};
        if (data.agentId) {
          const agentRes = await getAgentDetail(data.agentId);
          agent = agentRes?.data || {};
        }
        setAgentDetail(agent); // 新增
        setFormData({
          ...defaultFormData,
          ...data,
          prompt: agent.instruction,
          knowledges: agent.knowledge_bases,
          abilityName: agent.name,
          abilityAgentId: data.agentId,
        });
      })
      .finally(() => setLoading(false));
  }, [id]);

  // 保存
  const handleSave = useCallback(async () => {
    setLoading(true);
    try {
      const agentParams = {
        ...agentDetail,
        name: formData.abilityName,
        instruction: formData.prompt,
        knowledge_bases: formData.knowledges,
      };
      // 1. 先更新智能体
      await updateAgent(agentDetail.id, agentParams);
      // 2. 再更新AI员工
      await updateEmployee(id, {
        name: formData.name,
        description: formData.description,
        tags: formData.tags,
        isPublic: formData.permission === 'public',
        agentId: formData.abilityAgentId,
      });

      Message.success('保存成功');
      navigate('/knowledge/aiStaff');
    } catch (e) {
      Message.error('保存失败');
    } finally {
      setLoading(false);
    }
  }, [formData, id, navigate, agentDetail]);

  if (loading) {
    return <Spin style={{ width: '100%', marginTop: 100 }} />;
  }

  return (
    <div className={styles.container}>
      <AiStaffBasic
        formData={formData}
        setFormData={setFormData}
        isEditing={isEditing}
      />
      <AiStaffSettings
        formData={formData}
        setFormData={setFormData}
        isEditing={isEditing}
      />
      <div
        style={{
          marginTop: 32,
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 16,
        }}
      >
        <Button type="secondary" onClick={() => navigate('/knowledge/aiStaff')}>
          取消
        </Button>
        <Button type="primary" onClick={handleSave} disabled={!formData.name}>
          保存
        </Button>
      </div>
    </div>
  );
}
