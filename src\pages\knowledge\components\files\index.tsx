import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Table,
  Button,
  Input,
  Typography,
  Space,
  Select,
  Modal,
  Form,
  Message,
  Tabs,
  Grid,
  Upload,
} from '@arco-design/web-react';
import { useNavigate, useRouteMatch, useLocation } from 'react-router-dom';
import IconSearch from '@/assets/knowledge/IconSearch.svg';
import IconFile from '@/assets/knowledge/IconFile.svg';
import IconKnowledge from '@/assets/knowledge/IconKnowledge.png';
import NotFileIcon from '@/assets/knowledge/NotFile.svg';
import styles from './style/index.module.less';
import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import { message } from 'antd';
import {
  getKnowledgeFiles,
  KnowledgeFilesParams,
  KnowledgeFilesParams2,
  createKnowledgeFile,
  deleteVectorKnowledgeFile,
  uploadFile,
  deleteDocumentKnowledgeFile,
  updateKnowledge,
  getKnowledgeFiles2,
} from '@/lib/services/knowledge-service';
import useLocale from '@/utils/useLocale';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import IconScreen from '@/assets/application/screen.svg';
import IconCloseTag from '@/assets/close.svg';
import uploadSvg from '@/assets/application/upload.png';
import TopIcon from '@/assets/top.svg';

const { Text } = Typography;
const Option = Select.Option;
const FormItem = Form.Item;
const TextArea = Input.TextArea;
const { Row, Col } = Grid;

interface FileItem {
  id: string;
  name: string;
  characters: string;
  time: string;
  dataSource: string;
  fileSource: string;
  fileId?: string;
  text?: string;
}

function FileList() {
  const locale = useLocale();
  const navigate = useNavigate();
  const location = useLocation();
  const { path } = useRouteMatch();
  const dispatch = useDispatch();
  const fileDetailMenuName = useSelector(
    (state: GlobalState) => state.fileDetailMenuName
  );
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [nextId, setNextId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const isMounted = useRef(true);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [deleteFileId, setDeleteFileId] = useState<string | null>(null);
  const [contentLength, setContentLength] = useState(0);
  const [questionLength, setQuestionLength] = useState(0);
  const [answerLength, setAnswerLength] = useState(0);
  const [ListType, setListType] = useState('list');
  const [isEditing, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // 添加分页相关状态
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [searchValue, setSearchValue] = useState<string>('');
  const pageSize = 20;
  const tableContainerRef = useRef<HTMLDivElement>(null);

  //数据库设置
  const [knowledgeName, setKnowledgeName] = useState('');
  const [knowledgeDisplayName, setKnowledgeDisplayName] = useState('');
  const [description, setDescription] = useState('');
  const [labels, setLabels] = useState<string[]>([]);
  const [uploadFileList, setUploadFileList] = useState([]);
  const [isUploading, setIsUploading] = useState(false);

  // 添加监听数据变化的函数
  const checkForChanges = useCallback(() => {
    const originalData = location.state?.knowledgeInfo;
    const hasNameChanged = knowledgeDisplayName !== originalData?.display_name;
    const hasDescChanged = description !== originalData?.description;
    const hasLabelsChanged =
      JSON.stringify(labels) !== JSON.stringify(originalData?.labels || []);

    setHasChanges(hasNameChanged || hasDescChanged || hasLabelsChanged);
  }, [
    knowledgeDisplayName,
    description,
    labels,
    location.state?.knowledgeInfo,
  ]);

  // 监听数据变化
  useEffect(() => {
    if (isEditing) {
      checkForChanges();
    }
  }, [isEditing, knowledgeDisplayName, description, labels, checkForChanges]);

  // 重置编辑状态时也重置变化状态
  const handleCancelEdit = () => {
    setIsEditing(false);
    setHasChanges(false);
    // 重置表单数据到初始状态
    setKnowledgeName(location.state?.knowledgeInfo?.name || '');
    setKnowledgeDisplayName(location.state?.knowledgeInfo?.display_name || '');
    setDescription(location.state?.knowledgeInfo?.description || '');
    setLabels(location.state?.knowledgeInfo?.labels || []);
  };

  // 处理搜索输入变化
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    setHasMore(true);
    setFiles([]);
  };

  const fetchData = useCallback(
    async (startId: string | null = null) => {
      if (loading || !hasMore || !isMounted.current) return;

      setLoading(true);
      setError(null);
      try {
        // 创建新的 AbortController
        abortControllerRef.current = new AbortController();

        if (location.state?.type === 'document') {
          const params2: KnowledgeFilesParams2 = {
            page: currentPage,
            size: pageSize,
          };

          if (searchValue) {
            params2.fileNames = [searchValue];
          }

          const response = await getKnowledgeFiles2(
            location.state?.knowledgeInfo?.name,
            params2
          );
          // 检查组件是否已卸载
          if (!isMounted.current) return;

          const transformedFiles2 = response.items.map((item) => ({
            id: item.file_id,
            name: item.file_name,
            username: item.user?.user_name,
            createTime: item.create_date,
            updateTime: item.create_date,
            characters: formatFileSize(item.file_name),
            time: new Date().toLocaleString(),
            dataSource: item.file_source,
            fileSource: item.file_source,
          }));

          setFiles((prevFiles) =>
            startId ? [...prevFiles, ...transformedFiles2] : transformedFiles2
          );
        } else {
          const params: KnowledgeFilesParams = {
            page: currentPage,
            size: pageSize,
            start_id: startId,
            with_vector: true,
            included_payloads: ['text', 'answer', 'dataSource'],
            search_pairs: [
              {
                key: 'text',
                value: searchValue,
              },
            ],
          };
          const response = await getKnowledgeFiles(
            location.state?.knowledgeInfo?.name,
            params
          );
          // 检查组件是否已卸载
          if (!isMounted.current) return;

          const transformedFiles = response.items.map((item) => ({
            id: item.id,
            name: item.data.fileName || item.data.text,
            characters: formatFileSize(item.data.text),
            time: new Date().toLocaleString(),
            dataSource: item.data.dataSource,
            fileSource: item.data.fileSource,
            fileId: item.data.fileId,
            text:
              location.state.type === 'document'
                ? item.data.text
                : item.data.answer,
          }));

          setFiles((prevFiles) =>
            startId ? [...prevFiles, ...transformedFiles] : transformedFiles
          );
          setNextId(response.next_id);
          setHasMore(!!response.next_id);
        }
      } catch (error) {
        // 检查是否是取消请求导致的错误
        if (error.name === 'AbortError') {
          console.log('请求已取消');
          return;
        }

        if (isMounted.current) {
          console.error('获取文件列表失败:', error);
          setError('获取文件列表失败');
        }
      } finally {
        if (isMounted.current) {
          setLoading(false);
        }
      }
    },
    [location.state?.type, location.state?.knowledgeInfo?.name, searchValue]
  );

  useEffect(() => {
    // 设置知识库名称
    setKnowledgeName(location.state?.knowledgeInfo?.name || '');
    setKnowledgeDisplayName(location.state?.knowledgeInfo?.display_name || '');
    setDescription(location.state?.knowledgeInfo?.description || '');
    setLabels(location.state?.knowledgeInfo?.labels || []);

    isMounted.current = true;
    fetchData();

    return () => {
      isMounted.current = false;
      // 取消所有未完成的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [location.state?.knowledgeInfo?.name, searchValue]);

  const [showBackToTop, setShowBackToTop] = useState(false);

  // 回到顶部
  const scrollToTop = useCallback(() => {
    if (!tableContainerRef.current) return;

    setShowBackToTop(false);

    tableContainerRef.current.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  // 滚动监听函数
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      if (!isMounted.current) return;

      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

      // 处理回到顶部按钮显示
      setShowBackToTop(scrollTop > 300);

      // 处理加载更多数据
      if (
        scrollHeight - scrollTop - clientHeight < 100 &&
        !loading &&
        hasMore
      ) {
        fetchData(nextId);
        setCurrentPage(currentPage + 1);
      }
    },
    [nextId, loading, hasMore, fetchData]
  );

  const formatFileSize = (text: string): string => {
    // 这里可以根据实际需求计算文件大小
    // 目前使用文本长度作为示例
    const size = text.length;
    if (size < 1024) {
      return `${size} B`;
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    } else {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  const columns = [
    {
      title: locale['menu.application.knowledge.file.name'],
      dataIndex: 'name',
      width: '30%',
      render: (text, record) => (
        <div
          key={`file-${record.id}`}
          className={styles.fileCell}
          onClick={(e) => {
            e.stopPropagation();
            gotoFolderDetail(record);
          }}
        >
          <span className={styles.fileIcon}>
            <IconFile />
          </span>
          <Text className={styles.fileName}>{text}</Text>
        </div>
      ),
    },
    {
      title: locale['menu.application.knowledge.file.createUser'],
      dataIndex: 'creator',
      width: '15%',
      render: (text, record) => (
        <Text className={styles.creatorText}>{record.username || 'admin'}</Text>
      ),
    },
    {
      title: locale['menu.application.knowledge.file.createTime'],
      dataIndex: 'time',
      width: '15%',
      render: (text, record) => (
        <Text key={`time-${record.id}`} className={styles.timeText}>
          {record.createTime || '-'}
        </Text>
      ),
    },
    {
      title: locale['menu.application.knowledge.file.updateTime'],
      dataIndex: 'updateTime',
      width: '15%',
      render: (text, record) => (
        <Text key={`update-time-${record.id}`} className={styles.timeText}>
          {record.updateTime || '-'}
        </Text>
      ),
    },
    {
      title: locale['operation'],
      width: '10%',
      render: (_, record) => (
        <Space
          key={`operations-${record.id}`}
          size={8}
          className={styles.operations}
        >
          <Button
            type="text"
            className={`${styles.operationBtn} ${styles.deleteBtn}`}
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(record);
            }}
          >
            {locale['deleteBut']}
          </Button>
        </Space>
      ),
    },
  ];

  const updateBreadcrumbData = (newBreadcrumb: {}) => {
    dispatch({
      type: 'update-breadcrumb-menu-name',
      payload: { breadcrumbMenuName: newBreadcrumb },
    });
  };

  const gotoFolderDetail = (item) => {
    const detailPath = '/knowledge/list/detail';
    const knowledgeDisplayName = location.state?.knowledgeInfo?.display_name || knowledgeName;
    
    // 设置多层面包屑：知识库名称 -> 文件名称
    const breadcrumbData = new Map<string, string>([
      ['/knowledge/list', knowledgeDisplayName],
      [detailPath, item.name],
    ]);
    updateBreadcrumbData(breadcrumbData);
    
    navigate(detailPath, {
      state: {
        name: item.name,
        type: location.state.type,
        id: item.id,
        characters: item.characters,
        time: item.time,
        dataSource: item.dataSource,
        fileSource: item.fileSource,
        fileId: item.fileId,
        text: item.text,
        knowledgeName: knowledgeName,
        knowledgeDisplayName: knowledgeDisplayName,
        username: item.username,
        createTime: item.createTime,
      },
    });
  };

  const handleDelete = async (record: FileItem) => {
    setDeleteFileId(record.id);
    setIsDeleteModalVisible(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      if (!deleteFileId) return;

      if (location.state?.type === 'document') {
        await deleteDocumentKnowledgeFile(knowledgeName, deleteFileId);
      } else {
        await deleteVectorKnowledgeFile(knowledgeName, deleteFileId);
      }

      Message.success('删除成功');
      // 更新文件列表
      setFiles((prevFiles) =>
        prevFiles.filter((file) => file.id !== deleteFileId)
      );
      setIsDeleteModalVisible(false);
      setDeleteFileId(null);
    } catch (error) {
      console.error('删除文件失败:', error);
      Message.error('删除失败，请重试');
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalVisible(false);
    setDeleteFileId(null);
  };

  const fileUpload = async (fileList) => {
    try {
      // 防止重复上传
      if (isUploading) {
        return;
      }

      // 更新文件列表状态
      setUploadFileList(fileList);

      // 获取最新添加的文件（最后一个文件）
      const file = fileList[fileList.length - 1];
      if (!file) {
        Message.error('请选择文件');
        return;
      }

      const maxSize = 512 * 1024; // 512KB
      if (file.originFile && file.originFile.size > maxSize) {
        Message.error('文件大小不能超过512KB');
        // 移除过大的文件
        setUploadFileList(fileList.slice(0, -1));
        return;
      }

      // 只在文件状态为 uploading 时处理
      if (file.status === 'uploading') {
        setIsUploading(true);
        
        // 将文件转换为 base64
        const reader = new FileReader();
        reader.readAsDataURL(file.originFile);

        reader.onload = async () => {
          const base64Data = reader.result as string;
          const fileData = {
            files: [
              {
                file_name: file.name,
                file_data: base64Data,
                file_source: 'user',
              },
            ],
          };

          try {
            await uploadFile(knowledgeName, fileData);
            Message.success('文件上传成功');
            // 关闭弹窗
            setIsModalVisible(false);
            // 清空上传文件列表
            setUploadFileList([]);
            // 重置状态并刷新列表
            setFiles([]);
            setNextId(null);
            setHasMore(true);
            await fetchData();
          } catch (error) {
            console.error('文件上传失败:', error);
            Message.error('文件上传失败，请重试');
            // 移除上传失败的文件
            setUploadFileList(fileList.slice(0, -1));
          } finally {
            setIsUploading(false);
          }
        };

        reader.onerror = () => {
          Message.error('文件读取失败，请重试');
          setIsUploading(false);
          // 移除读取失败的文件
          setUploadFileList(fileList.slice(0, -1));
        };
      }
    } catch (error) {
      console.error('文件处理失败:', error);
      Message.error('文件处理失败，请重试');
      setIsUploading(false);
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validate();
      await createKnowledgeFile(knowledgeName, {
        text: values.content || values.question,
        data_source: 'user',
        payload:
          location.state?.type === 'document' ? {} : { answer: values.answer },
      });
      setIsModalVisible(false);
      form.resetFields();
      Message.success('创建文件成功');
      // 刷新文件列表
      setFiles([]);
      setNextId(null);
      setHasMore(true);
      fetchData();
    } catch (error) {
      console.error('表单验证失败:', error);
      Message.error('创建文件失败，请重试');
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    // 清空上传文件列表
    setUploadFileList([]);
    setIsUploading(false);
  };

  const handleUpdateKnowledge = async () => {
    try {
      setLoading(true);

      const res = await updateKnowledge(knowledgeName, {
        display_name: knowledgeDisplayName,
        description: description,
        labels: labels,
      });

      if (res) {
        Message.success('更新成功');
        // 更新路由状态
        navigate(location.pathname, {
          replace: true,
          state: {
            ...location.state,
            knowledgeInfo: {
              ...location.state?.knowledgeInfo,
              display_name: knowledgeDisplayName,
              description: description,
              labels: labels,
            },
          },
        });
        // 重置状态并刷新数据
        setFiles([]);
        setNextId(null);
        setHasMore(true);
        await fetchData();
      } else {
        Message.error('更新失败');
      }
    } catch (error) {
      console.error('更新知识库失败:', error);
      Message.error('更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理问题变化
  const handleQuestionChange = (value) => {
    setQuestionLength(value.length);
  };

  // 处理答案变化
  const handleAnswerChange = (value) => {
    setAnswerLength(value.length);
  };

  const uploadModal = (
    <div>
      {location.state?.type === 'document' ? (
        <Modal
          visible={isModalVisible}
          onCancel={handleModalCancel}
          footer={null}
          maskClosable={false}
          className={styles.uploadModal}
          style={{ width: '640px', height: '320px', borderRadius: '8px' }}
        >
          <div className={styles.title}>{locale['upload']}</div>

          <div className={styles.uploadContainer}>
            <Upload
              drag
              accept=".txt"
              fileList={uploadFileList}
              showUploadList={false}
              onChange={fileUpload}
              disabled={isUploading}
            >
              <div className={styles.uploadContent}>
                <div className={styles.uploadTitle}>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px',
                    }}
                  >
                    <img
                      src={uploadSvg}
                      className={styles.uploadIcon}
                      style={{ display: 'block' }}
                    />
                    <span
                      style={{
                        fontWeight: '500',
                        fontSize: '16px',
                        color: '#4455F2',
                      }}
                    >
                      {
                        locale[
                          'menu.application.knowledge.file.upload.clickOrDrag'
                        ]
                      }
                    </span>
                  </div>
                </div>
                <div className={styles.uploadSubtitle}>
                  {locale['menu.application.knowledge.file.upload.support']}
                  <br />
                </div>
              </div>
            </Upload>
          </div>
        </Modal>
      ) : (
        <Modal
          title={locale['menu.application.knowledge.file.createQanda']}
          visible={isModalVisible}
          onOk={handleModalOk}
          onCancel={handleModalCancel}
          autoFocus={false}
          maskClosable={false}
          okText={locale['createBut']}
          cancelText={locale['cancelBut']}
          className={styles.uploadModal}
          okButtonProps={{
            className: styles.submitBtn,
          }}
          cancelButtonProps={{
            className: styles.cancelBtn,
          }}
        >
          <Form form={form} layout="vertical">
            <Form.Item
              label="问题"
              field="question"
              rules={[{ required: true, message: '请输入问题' }]}
            >
              <Input.TextArea
                placeholder="请输入"
                onChange={handleQuestionChange}
                showWordLimit
                maxLength={200}
                className={styles.inputQuestionTextArea}
              />
            </Form.Item>
            <Form.Item
              label="答案"
              field="answer"
              rules={[{ required: true, message: '请输入答案' }]}
            >
              <Input.TextArea
                placeholder="请输入"
                onChange={handleAnswerChange}
                showWordLimit
                maxLength={2000}
                className={styles.inputTextArea}
              />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </div>
  );

  return (
    <div className={styles.contentSection}>
      <div className={styles.titleSection}>
        <Tabs
          className={styles.tabs}
          activeTab={ListType}
          onChange={setListType}
        >
          <TabPane
            key="list"
            title={
              location.state?.type === 'document'
                ? locale['menu.application.knowledge.file.list']
                : locale['menu.application.knowledge.file.list.question']
            }
          ></TabPane>

          <TabPane
            key="setting"
            title={locale['menu.application.knowledge.file.setting']}
          ></TabPane>
        </Tabs>
      </div>
      {ListType === 'list' ? (
        <div className={styles.fileList}>
          <RowComponent className={styles.header}>
            <RowComponent>
              <Button
                type="primary"
                className={styles.addBtn}
                onClick={() => {
                  setIsModalVisible(true);
                  // 重置上传状态
                  setUploadFileList([]);
                  setIsUploading(false);
                }}
              >
                {location.state?.type === 'document'
                  ? locale['menu.application.knowledge.file.upload']
                  : locale['menu.application.knowledge.file.upload.question']}
              </Button>
            </RowComponent>

            <RowComponent className={styles.rowEndCenter}>
              <Text className={styles.countAppText}>
                共 {files.length} 个文件
              </Text>
              <Input
                className={styles.searchBox}
                prefix={<IconSearch />}
                placeholder={
                  locale['menu.application.knowledge.file.search.placeholder']
                }
                value={searchValue}
                onChange={handleSearchChange}
                allowClear
              />
            </RowComponent>

            <div className={styles.bar}>
              <div className={styles.name}>
                <span>{locale['menu.application.knowledge.file.name']}</span>
              </div>
              <div className={styles.creator}>
                <span>
                  {locale['menu.application.knowledge.file.createUser']}
                </span>
              </div>
              <div className={styles.creation_time}>
                <span>
                  {locale['menu.application.knowledge.file.createTime']}
                </span>
              </div>
              <div className={styles.turnover_time}>
                <span>
                  {locale['menu.application.knowledge.file.updateTime']}
                </span>
              </div>
              <div className={styles.controls}>
                <span>{locale['operation']}</span>
              </div>
            </div>
          </RowComponent>

          <main
            className={styles.tableContainer}
            ref={tableContainerRef}
            onScroll={handleScroll}
            style={{ height: 'calc(100vh - 200px)', overflow: 'auto' }}
          >
            <Table
              columns={columns}
              data={files}
              loading={loading}
              rowKey="id"
              showHeader={false}
              border={{
                wrapper: true,
                cell: true,
              }}
              scroll={{
                y: '100%',
              }}
              className={styles.table}
              pagination={false}
              noDataElement={
                <div className={styles.emptyContainer}>
                  <NotFileIcon />
                  <Text className={styles.emptyText}>
                    {locale['menu.application.knowledge.file.empty']}
                  </Text>
                </div>
              }
            />
            {loading && (
              <div className={styles.loading}>{locale['loading']}</div>
            )}
          </main>
          {uploadModal}
          <Modal
            title={locale['menu.application.knowledge.file.delete']}
            visible={isDeleteModalVisible}
            onOk={handleDeleteConfirm}
            onCancel={handleDeleteCancel}
            autoFocus={false}
            maskClosable={false}
            okText={locale['confirmBut']}
            cancelText={locale['cancelBut']}
            className={styles.deleteModal}
            okButtonProps={{
              className: styles.submitBtn,
            }}
            cancelButtonProps={{
              className: styles.cancelBtn,
            }}
          >
            <p>{locale['menu.application.knowledge.file.delete.confirm']}</p>
          </Modal>
        </div>
      ) : (
        <div className={styles.settingsSection}>
          <div style={{ flex: 1 }}>
            <RowComponent style={{ marginTop: 16 }}>
              <div className={styles.iconContainer}>
                <div className={styles.iconKnowledge}>
                  <img src={IconKnowledge} alt="IconKnowledge" />
                </div>

                <div className={styles.divider}></div>

                {/* 名称 */}
                <div className={styles.nameContainer}>
                  <RowComponent>
                    <Text className={`${styles.subtitle} ${styles.required}`}>
                      {locale['menu.application.info.basic.names']}
                    </Text>
                  </RowComponent>
                  <RowComponent style={{ marginTop: 8, width: '100%' }}>
                    <Input
                      allowClear
                      placeholder={'请输入'}
                      style={{
                        backgroundColor: '#00000003',
                        border: '1px solid #00000014',
                        borderRadius: '8px',
                        height: '40px',
                      }}
                      value={knowledgeDisplayName}
                      onChange={(value) => {
                        setKnowledgeDisplayName(value);
                      }}
                      disabled={!isEditing}
                    />
                  </RowComponent>
                </div>
              </div>
            </RowComponent>

            {/* 描述 */}
            <RowComponent style={{ marginTop: 16 }}>
              <Text className={styles.subtitle}>
                {locale['menu.application.info.basic.descript']}
              </Text>
            </RowComponent>
            <RowComponent style={{ marginTop: 8 }}>
              <div style={{ position: 'relative', width: '100%' }}>
                <TextArea
                  placeholder={'请输入'}
                  maxLength={100}
                  value={description}
                  onChange={(value) => {
                    setDescription(value);
                  }}
                  style={{
                    backgroundColor: '#00000003',
                    border: '1px solid #00000014',
                    width: '100%',
                    resize: 'none',
                    height: '64px',
                    borderRadius: '8px',
                  }}
                  disabled={!isEditing}
                />
                <div
                  style={{
                    position: 'absolute',
                    bottom: '8px',
                    right: '8px',
                    fontSize: '12px',
                    color: 'rgba(0, 0, 0, 0.45)',
                    pointerEvents: 'none',
                  }}
                >
                  {description.length}/100
                </div>
              </div>
            </RowComponent>

            {/* 标签 */}
            <RowComponent className={styles.titleRow}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.basic.label']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {locale['menu.application.info.basic.placeholder.label']}
                </Text>
              </div>
              {isEditing && (
                <Button
                  className={styles.addLabelBut}
                  disabled={labels.length >= 3}
                  onClick={() => {
                    if (labels.length >= 3) {
                      return;
                    }
                    setLabels([...labels, '']);
                  }}
                  style={{
                    opacity: labels.length >= 3 ? 0.5 : 1,
                    cursor: labels.length >= 3 ? 'not-allowed' : 'pointer',
                  }}
                >
                  <Text className={styles.operateText}>
                    {locale['menu.application.template.setting.adds']}
                  </Text>
                </Button>
              )}
            </RowComponent>
            <Col span={24} className={styles.labelContainer}>
              {/* 渲染已选择的标签 */}
              {labels.length > 0 && (
                <div className={styles.selectedItemList}>
                  {labels.map((label, index) => (
                    <Row
                      key={`${label}-${index}`}
                      className={styles.selectedItemRow}
                    >
                      <Input
                        value={label}
                        autoFocus={true}
                        onChange={(value) => {
                          if (value && value.length > 10) {
                            return;
                          }
                          const newLabels = [...labels];
                          newLabels[index] = value;
                          setLabels(newLabels);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            e.stopPropagation();
                          }
                        }}
                        placeholder={locale[
                          'menu.application.create.group.form.label.placeholder'
                        ].replace('{index}', (index + 1).toString())}
                        suffix={
                          isEditing && (
                            <IconCloseTag
                              className={styles.deleteIcon}
                              onClick={() => {
                                const newLabels = [...labels];
                                newLabels.splice(index, 1);
                                setLabels(newLabels);
                              }}
                            />
                          )
                        }
                        className={styles.selectedItemCol}
                        disabled={!isEditing}
                      />
                    </Row>
                  ))}
                </div>
              )}
            </Col>

            <RowComponent className={styles.operateButGroup}>
              {!isEditing ? (
                <Button
                  type="primary"
                  className={[styles.createBut, styles.but]}
                  onClick={() => setIsEditing(true)}
                >
                  {locale['editBut']}
                </Button>
              ) : (
                <>
                  <Button
                    type="primary"
                    className={`${styles.createBut} ${styles.but} ${
                      !knowledgeName || !hasChanges ? styles.disabled : ''
                    }`}
                    onClick={handleUpdateKnowledge}
                    loading={loading}
                    disabled={!knowledgeName || !hasChanges}
                  >
                    {locale['updateBut']}
                  </Button>
                  <Button
                    className={[styles.cancelBut, styles.but]}
                    onClick={handleCancelEdit}
                  >
                    {locale['cancelBut']}
                  </Button>
                </>
              )}
            </RowComponent>
          </div>
        </div>
      )}

      <div
        className={`${styles.backToTop} ${!showBackToTop ? styles.hidden : ''}`}
        onClick={scrollToTop}
      >
        <TopIcon />
        <span>{locale['backToTop']}</span>
      </div>
    </div>
  );
}

export default FileList;
