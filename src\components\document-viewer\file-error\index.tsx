import { Alert, Flex } from 'antd';

import { useTranslate } from '@/hooks/common-hooks';
import React from 'react';

const FileError = ({ children }: React.PropsWithChildren) => {
  return (
    <Flex align="center" justify="center" className={'h-full w-full'}>
      <Alert
        type="error"
        message={<h2>{children || '文件错误'}</h2>}
      ></Alert>
    </Flex>
  );
};

export default FileError;
