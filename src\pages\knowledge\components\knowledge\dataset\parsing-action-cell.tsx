import { useShowDeleteConfirm } from '../hooks/common-hooks';
import { useRemoveNextDocument } from '../hooks/document-hooks';
import { IDocumentInfo } from '../interfaces/database/document';
import { downloadDocument } from '../utils/file-util';
import {
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  ToolOutlined,
} from '@ant-design/icons';
import { Button, Dropdown, MenuProps, Space, Tooltip, Popconfirm } from 'antd';
import { Popover } from '@arco-design/web-react';
import { MoreOutlined } from '@ant-design/icons';
import { isParserRunning } from './utils';
import { useCallback } from 'react';
import { DocumentType } from '../constants/knowledge';
import { iconMap, iconTipTextMap } from './parsing-status-cell';
import { useHandleRunDocumentByIds } from './hooks';

interface IProps {
  record: IDocumentInfo;
  setCurrentRecord: (record: IDocumentInfo) => void;
  showRenameModal: () => void;
  showChangeParserModal: () => void;
  showSetMetaModal: () => void;
}

const ParsingActionCell = ({
  record,
  setCurrentRecord,
  showRenameModal,
  showChangeParserModal,
  showSetMetaModal,
}: IProps) => {
  const documentId = record.id;
  const isRunning = isParserRunning(record.run);
  const { removeDocument } = useRemoveNextDocument();
  const showDeleteConfirm = useShowDeleteConfirm();
  const isVirtualDocument = record.type === DocumentType.Virtual;

  const onRmDocument = () => {
    if (!isRunning) {
      showDeleteConfirm({
        onOk: () => removeDocument([documentId]),
        content: record?.parser_config?.graphrag?.use_graphrag
          ? '该文档与知识图谱相关联。删除后，相关节点和关系信息将被删除，但图不会立即更新。更新图动作是在解析承载知识图谱提取任务的新文档的过程中执行的。'
          : '',
      });
    }
  };

  const onDownloadDocument = () => {
    downloadDocument({
      id: documentId,
      filename: record.name,
    });
  };

  const setRecord = useCallback(() => {
    setCurrentRecord(record);
  }, [record, setCurrentRecord]);

  const onShowRenameModal = () => {
    setRecord();
    showRenameModal();
  };
  const onShowChangeParserModal = () => {
    setRecord();
    showChangeParserModal();
  };

  const onShowSetMetaModal = useCallback(() => {
    setRecord();
    showSetMetaModal();
  }, [setRecord, showSetMetaModal]);

  const chunkItems: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <div className="flex flex-col">
          <Button type="link" onClick={onShowChangeParserModal}>
            {'切片方法'}
          </Button>
        </div>
      ),
    },
    { type: 'divider' },
    {
      key: '2',
      label: (
        <div className="flex flex-col">
          <Button type="link" onClick={onShowSetMetaModal}>
            {'设置元数据'}
          </Button>
        </div>
      ),
    },
  ];

  const text = record.run;

  const { handleRunDocumentByIds } = useHandleRunDocumentByIds(record.id);

  const handleOperationIconClick =
    (shouldDelete = false) =>
    () => {
      handleRunDocumentByIds(record.id, isRunning, shouldDelete);
    };

  const OperationIcon = iconMap[text];
  const OperationIconTipText = iconTipTextMap[text];

  return (
    <Space size={0}>
      <Popconfirm
        title={`是否清空已有 ${record.chunk_num}个 chunk？`}
        onConfirm={handleOperationIconClick(true)}
        onCancel={handleOperationIconClick(false)}
        disabled={record.chunk_num === 0}
        okText={'是'}
        cancelText={'否'}
      >
        <Button
          type="text"
          className={'!p-[4px_8px]'}
          onClick={
            record.chunk_num === 0 ? handleOperationIconClick(false) : () => {}
          }
        >
          {OperationIcon && (
            <Tooltip title={OperationIconTipText}>{<OperationIcon />}</Tooltip>
          )}
        </Button>
      </Popconfirm>
      {/* {isVirtualDocument || (
        <Dropdown
          menu={{ items: chunkItems }}
          trigger={['click']}
          disabled={isRunning || record.parser_id === 'tag'}
        >
          <Button type="text" className={'!p-[4px_8px]'}>
            <ToolOutlined size={20} />
          </Button>
        </Dropdown>
      )} */}
      <Popover
        position="bl"
        trigger="hover"
        className="[&_.arco-popover-content]:p-[8px] w-[160px]"
        content={
          <>
            {isVirtualDocument || (
              <Button
                type="text"
                block
                className={`min-w-[144px] text-left `}
                disabled={isRunning}
                onClick={onShowChangeParserModal}
              >
                编辑
              </Button>
            )}
            {/* <Button
              type="text"
              block
              className={`min-w-[144px] text-left `}
              disabled={isRunning}
              onClick={onShowRenameModal}
            >
              重命名
            </Button> */}
            <Button
              type="text"
              block
              className={`min-w-[144px] text-left `}
              onClick={onRmDocument}
            >
              删除
            </Button>
          </>
        }
      >
        <Button
          type="text"
          className="rounded-[8px] border border-[#f5f5f5]"
          icon={<MoreOutlined />}
        ></Button>
      </Popover>

      {/* 
      <Tooltip title={'重命名'}>
        <Button
          type="text"
          disabled={isRunning}
          onClick={onShowRenameModal}
          className={'!p-[4px_8px]'}
        >
          <EditOutlined size={20} />
        </Button>
      </Tooltip>
      <Tooltip title={'删除'}>
        <Button
          type="text"
          disabled={isRunning}
          onClick={onRmDocument}
          className={'!p-[4px_8px]'}
        >
          <DeleteOutlined size={20} />
        </Button>
      </Tooltip>
      {isVirtualDocument || (
        <Tooltip title={'下载'}>
          <Button
            type="text"
            disabled={isRunning}
            onClick={onDownloadDocument}
            className={'!p-[4px_8px]'}
          >
            <DownloadOutlined size={20} />
          </Button>
        </Tooltip>
      )} */}
    </Space>
  );
};

export default ParsingActionCell;
