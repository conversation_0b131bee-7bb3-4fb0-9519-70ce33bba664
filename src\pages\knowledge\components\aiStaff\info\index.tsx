import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Tabs, Message, Spin } from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import localStyles from './style/index.module.less';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import ApplicationBasic from './components/basic';
import ApplicationSettings from './components/settings';
import ApplicationTesting from './components/testing';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  getAgentDetail,
  updateAgent,
  createAgent,
  AgentResponse,
  bindAcpTools,
} from '@/lib/services/agent-service';
import { getAcpToolsById } from '@/lib/services/acp-server-service';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import ButtonComponent from '@arco-design/web-react/es/Button';
import Text from '@arco-design/web-react/es/Typography/text';
import {
  createEmployee,
  getEmployeeDetail,
} from '@/pages/knowledge/components/knowledge/services/aiStaff-service';
import { updateEmployee } from '@/pages/knowledge/components/knowledge/services/aiStaff-service';

function AiStaffInfo() {
  const locale = useLocale();
  const navigate = useNavigate();
  const location = useLocation<{ id: string; path: string; atId: string }>();
  const { id, path, atId } = location.state || {};
  const [loading, setLoading] = useState(false);
  const [agentData, setAgentData] = useState<AgentResponse | null>(null);
  const [newAgentData, setNewAgentData] = useState<AgentResponse | null>(null);
  const [newEmployeeData, setNewEmployeeData] =
    useState<EmployeeResponse | null>(null);
  const [lastFetchedId, setLastFetchedId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('agentInfo');
  const [isEditing, setIsEditing] = useState(true);
  const [isFormValid, setIsFormValid] = useState(false);
  const [creating, setCreating] = useState(false);
  const [employeeId, setEmployeeId] = useState<string | null>(null);
  // 新增：基础信息表单数据
  const [basicFormData, setBasicFormData] = useState({
    name: '',
    description: '',
    labels: [],
    isPublic: false,
  });

  // 新增：basic表单数据回调
  const handleAgentBasicUpdate = useCallback((data) => {
    setBasicFormData((prev) => ({ ...prev, ...data }));
  }, []);

  const currentAgentIdRef = useRef(id);
  const currentPathRef = useRef(path);
  const currentAtIdRef = useRef(atId);

  // 验证表单是否有效
  const validateForm = useCallback((data: AgentResponse | null) => {
    if (!data) {
      return false;
    }

    // 智能体类型校验
    if (!data.type) {
      return false;
    }

    // 模型选择校验
    const llmConfig = data.llm_config;
    if (llmConfig && !llmConfig.is_inherit) {
      const hasProvider =
        llmConfig.provider && llmConfig.provider.trim() !== '';
      const hasModel = llmConfig.model && llmConfig.model.trim() !== '';

      if (hasProvider !== hasModel) {
        return false;
      }
    }

    return true;
  }, []);

  const fetchEmployeeDetail = useCallback(async () => {
    if (loading && currentAgentIdRef.current === lastFetchedId) {
      return;
    }

    try {
      setLoading(true);
      if (!currentAgentIdRef.current) {
        setIsEditing(true);
        return;
      }
      const response = await getEmployeeDetail(currentAgentIdRef.current);
      setEmployeeId(response.data.id);

      setBasicFormData({
        name: response.data.name || '',
        description: response.data.description || '',
        labels: response.data.tags || [],
        isPublic: response.data.isPublic || false,
      });

      setAgentData(response.data.agent);
      setNewAgentData(response.data.agent);
      setNewEmployeeData(response.data.agent);
      setLastFetchedId(currentAgentIdRef.current);

      setIsFormValid(validateForm(response.data));
    } catch (error) {
      Message.error({
        content: locale['menu.application.opreate.errMsg'],
      });
    } finally {
      setLoading(false);
    }
  }, [currentAgentIdRef.current, loading, lastFetchedId]);

  const handleAgentDataUpdate = useCallback(
    (newData: Partial<AgentResponse>) => {
      setNewAgentData((prev) => {
        if (!prev) {
          return {
            id: '',
            name: newData.name || 'New Agent',
            description: newData.description || 'New Agent Description',
            type: 'task',
            instruction: '',
            templates: [],
            functions: [],
            responses: [],
            samples: [],
            utilities: [],
            knowledge_bases: [],
            rules: [],
            is_public: false,
            is_host: false,
            disabled: false,
            icon_url: newData.icon_url || null,
            profiles: [],
            routing_rules: [],
            llm_config: {
              is_inherit: false,
              provider: null,
              model: null,
              max_recursion_depth: 3,
              max_tokens: null,
              temperature: null,
            },
            workflowId: '',
            applicationId: '',
            labels: newData.labels || [],
            ts_cards: newData.ts_cards || [],
            acp_tools: newData.acp_tools || [],
          } as AgentResponse;
        }

        const updatedData = { ...prev, ...newData };

        // 验证更新后的数据
        const isValid = validateForm(updatedData);
        setIsFormValid(isValid);

        return updatedData;
      });
    },
    [validateForm]
  );

  useEffect(() => {
    const initData = async () => {
      setIsEditing(false);
      if (currentAgentIdRef.current) {
        await fetchEmployeeDetail();
        setIsEditing(true);
      } else {
        setLoading(false);
        setIsEditing(true);

        setIsFormValid(validateForm(null));
      }
    };

    initData();
  }, []);

  const handleAgentIdChange = (newId: string) => {
    currentAgentIdRef.current = newId;
  };

  const handleCancel = () => {
    if (isEditing && agentData?.id) {
      setIsEditing(false);

      const oldAgentData = { ...agentData };
      setNewAgentData(oldAgentData);
    } else {
      if (currentPathRef.current === 'agentTeam') {
        navigate('/application/info', {
          state: { id: currentAtIdRef.current, path: 'settings' },
        });
      } else if (currentPathRef.current === 'routing') {
        navigate('/application/info', {
          state: { id: currentAtIdRef.current, path: 'routing' },
        });
      } else {
        navigate('/agent');
      }
    }
  };

  const handleEdit = () => {
    handleSave();
  };

  // 创建AI员工和Agent的handleSave逻辑
  const handleSave = useCallback(async () => {
    setCreating(true);
    try {
      if (newAgentData?.id && isEditing) {
        // 编辑模式
        // 1. 组装Agent参数，name取selectedAbilityName
        const agentParams = {
          ...newAgentData,
          name: agentData.name, // selectedAbilityName已同步到basicFormData.name
          instruction: agentData?.instruction,
          knowledge_bases: (agentData?.knowledge_bases || []).map((kb) => ({
            name: kb.name,
            type: kb.type,
            disabled: kb.disabled,
          })),
        };
        await updateAgent(agentData?.agent_id, agentParams as any);
        // 2. 组装Employee参数，不包含name/selectedAbilityName
        const employeeParams = {
          name: basicFormData.name,
          description: basicFormData.description,
          tags: basicFormData.labels,
          isPublic: basicFormData.isPublic,
          // 其他Employee字段如有
        };
        await updateEmployee(newEmployeeData.id, employeeParams);
        Message.success('保存成功');
        navigate('/knowledge/aiStaff');
      } else {
        // 新建模式
        // 1. 明确组装 agentParams，确保字段为当前编辑内容
        const agentParams = {
          ...newAgentData,
          name: basicFormData.name, // selectedAbilityName已同步到basicFormData.name
          instruction: newAgentData?.instruction,
          knowledge_bases: (newAgentData?.knowledge_bases || []).map((kb) => ({
            name: kb.name,
            type: kb.type,
            disabled: kb.disabled,
          })),
        };
        // 2. 创建新agent
        const agentRes = await createAgent(agentParams as any);
        const agentId = agentRes?.agent_id || agentRes?.id;
        if (!agentId) throw new Error('智能体创建失败');
        // 3. 创建AI员工，基础信息字段取自basicFormData
        await createEmployee({
          name: basicFormData.name,
          description: basicFormData.description,
          tags: basicFormData.labels,
          isPublic: basicFormData.isPublic,
          agentId,
        });
        Message.success(locale['opreate.create.success'] || '创建成功');
        navigate('/knowledge/aiStaff');
      }
    } catch (e) {
      Message.error(locale['opreate.create.errMsg'] || '保存失败');
    } finally {
      setCreating(false);
    }
  }, [newAgentData, basicFormData, navigate, locale]);

  if (loading) {
    return (
      <div
        className={localStyles.customContainer}
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px',
        }}
      >
        <Spin dot size={40} />
      </div>
    );
  }

  return (
    <div className={localStyles.customContainer}>
      <Tabs
        className={localStyles.tabs}
        activeTab={activeTab}
        onChange={setActiveTab}
      >
        {/* 应用页面内容 */}
        <TabPane
          key="agentInfo"
          title={locale['menu.application.info.header.basic']}
        >
          <ApplicationBasic
            agentData={newAgentData}
            loading={loading}
            isEditing={isEditing}
            onAgentDataUpdate={handleAgentDataUpdate}
            onAgentBasicUpdate={handleAgentBasicUpdate}
            basicData={basicFormData}
          />
        </TabPane>
        <TabPane
          key="agentSettings"
          title={locale['menu.application.info.header.settings']}
        >
          <ApplicationSettings
            agentData={newAgentData}
            loading={loading}
            isEditing={isEditing}
            onAgentDataUpdate={handleAgentDataUpdate}
            newEmployeeData={newEmployeeData}
          />
        </TabPane>
      </Tabs>

      {(activeTab === 'agentInfo' || activeTab === 'agentSettings') && (
        <div className={localStyles.footer}>
          <RowComponent className={localStyles.operateButGroup}>
            <ButtonComponent
              type="secondary"
              className={[localStyles.cancelBut, localStyles.but]}
              onClick={handleCancel}
            >
              <Text className={localStyles.text}>
                {locale['menu.application.template.setting.operate.cancel']}
              </Text>
            </ButtonComponent>
            {newAgentData?.id ? (
              <>
                {isEditing && (
                  <>
                    {isEditing ? (
                      <ButtonComponent
                        loading={loading}
                        onClick={handleEdit}
                        type="primary"
                        className={[localStyles.createBut, localStyles.but]}
                      >
                        <Text
                          className={localStyles.text}
                          style={{ color: '#FFFFFF' }}
                        >
                          {locale['editBut']}
                        </Text>
                      </ButtonComponent>
                    ) : (
                      <ButtonComponent
                        loading={loading}
                        onClick={handleSave}
                        type="primary"
                        disabled={!isFormValid || creating}
                        className={[
                          localStyles.createBut,
                          localStyles.but,
                          (!isFormValid || creating) && localStyles.disabled,
                        ]}
                      >
                        <Text
                          className={localStyles.text}
                          style={{ color: '#FFFFFF' }}
                        >
                          {locale['menu.application.opreate.save']}
                        </Text>
                      </ButtonComponent>
                    )}
                  </>
                )}
              </>
            ) : (
              <ButtonComponent
                loading={creating}
                onClick={handleSave}
                type="primary"
                disabled={!isFormValid || creating}
                className={[
                  localStyles.createBut,
                  localStyles.but,
                  (!isFormValid || creating) && localStyles.disabled,
                ]}
              >
                <Text className={localStyles.text} style={{ color: '#FFFFFF' }}>
                  {locale['menu.application.opreate.create']}
                </Text>
              </ButtonComponent>
            )}
          </RowComponent>
        </div>
      )}
    </div>
  );
}

export default AiStaffInfo;
