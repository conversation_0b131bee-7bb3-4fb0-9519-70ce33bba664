// AI员工管理接口服务

import axiosInstance from '@/lib/services/interceptors';
import { IFetchEmployeeListRequestParams } from '@/pages/knowledge/components/file-manager/src/interfaces/request/aiStaff';
import request from '@/pages/knowledge/components/file-manager/src/utils/request';


// export const listEmployees = (
//   params?: {
//       'Pager.Page': params.Pager?.Page || 1,
//         'Pager.Size': params.Pager?.Size || 16,
//     Name?: string;
//     Disabled?: boolean | null;
//     Tags?: string;
//     'Pager.Sort'?: string;
//     'Pager.Order'?: string;
//     IsPublic?: boolean;
//     ExcludePersonalCreated?: boolean;
//   }
// ) => request.get('/api/employee/manage-employees', { params });

export interface EmployeeListParams {
  Pager?: {
    Page: number;
    Size: number;
  };
  Name?: string;
  Disabled?: boolean | null;
  Tags?: string;
  'Pager.Sort'?: string;
  'Pager.Order'?: string;
  IsPublic?: boolean;
  CreateUserId?: string;
}

export interface EmployeeListResponse {
  items: any[];
  count: number;
}

export async function listEmployees(
  params: EmployeeListParams
): Promise<EmployeeListResponse> {
  try {
    const response = await axiosInstance.get('/api/employee/manage-employees', {
      params: {
        CreateUserId: params.CreateUserId || '',
        'Pager.Page': params.Pager?.Page || 1,
        'Pager.Size': params.Pager?.Size || 16,
        Name: params?.Name || '',
      },
    });
    return response.data;
  } catch (error) {
    console.error('获取AI员工列表失败:', error);
    throw error;
  }
}


export const listEmployeesall = (
  params?: {
    Pager?: {
      Page: number;
      Size: number;
    };
    Name?: string;
    Disabled?: boolean | null;
    Tags?: string;
    'Pager.Sort'?: string;
    'Pager.Order'?: string;
    IsPublic?: boolean;
    ExcludePersonalCreated?: boolean;
  }
) => axiosInstance.get('/api/employees', {
  params: {
    ExcludePersonalCreated: false,
    'Pager.Page': params.Pager?.Page || 1,
    'Pager.Size': params.Pager?.Size || 16,
    IsPublic: true,
    Disabled: false,
    Name: params?.Name || '',
  }
});


// 启用AI员工
export const enableEmployee = (employeeId: string) =>
  request.put(`/api/employee/${employeeId}/enable`);

// 禁用AI员工
export const disableEmployee = (employeeId: string) =>
  request.put(`/api/employee/${employeeId}/disable`);

// 删除AI员工
export const deleteEmployee = (employeeId: string) =>
  request.delete(`/api/employee/${employeeId}`);

// 创建AI员工
export const createEmployee = (data: {
  name?: string;
  description?: string;
  tags?: string[];
  isPublic?: boolean;
  agentId?: string | null;
}) => axiosInstance.post('/api/employee', data);

// 获取知识库列表
export const fetchKnowledgeCollections = (params?: {
  KeyWord?: string;
  Type?: string;
  Labels?: string[];
}) => request.get('/api/knowledge/vector/collections', { params });


export const getEmployeeDetail = (employeeId: string) =>
  request.get(`/api/employee/${employeeId}`);

export const updateEmployee = (employeeId: string, data: any) =>
  axiosInstance.put(`/api/employee/${employeeId}`, data);


