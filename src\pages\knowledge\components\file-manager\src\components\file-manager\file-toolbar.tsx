import { useTranslate } from '@/pages/knowledge/components/file-manager/src/hooks/common-hooks';
import {
  IListResult,
  useFetchParentFolderList,
} from '@/pages/knowledge/components/file-manager/src/hooks/file-manager-hooks';
import {
  DownOutlined,
  FileTextOutlined,
  FolderOpenOutlined,
  PlusOutlined,
  SearchOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons';
import {
  Breadcrumb,
  BreadcrumbProps,
  Button,
  Dropdown,
  Flex,
  Input,
  MenuProps,
  Space,
} from 'antd';
import { useCallback, useMemo } from 'react';
import {
  useHandleBreadcrumbClick,
  useHandleDeleteFile,
  useSelectBreadcrumbItems,
} from './hooks';
import { useGetFolderId } from '@/pages/knowledge/components/file-manager/src/hooks/file-manager-hooks';

import { FolderInput, Trash2 } from 'lucide-react';
import styles from './index.module.less';
import ReturnIcon from '@/assets/knowledge/ReturnIcon.svg';

interface IProps
  extends Pick<IListResult, 'searchString' | 'handleInputChange'> {
  selectedRowKeys: string[];
  showFolderCreateModal: () => void;
  showFileUploadModal: () => void;
  setSelectedRowKeys: (keys: string[]) => void;
  showMoveFileModal: (ids: string[]) => void;
  total?: number;
  isBatchMode: boolean;
  onEnterBatchMode: () => void;
  onExitBatchMode: () => void;
  dataSource: { id: string; name: string }[]; // 新增参数，传递当前所有可见文件/文件夹列表
}

const FileToolbar = ({
  selectedRowKeys,
  showFolderCreateModal,
  showFileUploadModal,
  setSelectedRowKeys,
  searchString,
  handleInputChange,
  showMoveFileModal,
  total = 0,
  isBatchMode,
  onEnterBatchMode,
  onExitBatchMode,
  dataSource,
}: IProps) => {
  const { t } = useTranslate('knowledgeDetails');
  const breadcrumbItems = useSelectBreadcrumbItems();
  const { handleBreadcrumbClick } = useHandleBreadcrumbClick();
  const parentFolderList = useFetchParentFolderList();
  const currentFolderId = useGetFolderId();
  const isKnowledgeBase =
    parentFolderList.at(-1)?.source_type === 'knowledgebase';

  // 判断是否为根目录（主页）
  const isRootDirectory = !currentFolderId;

  // 返回主页
  const handleBackToHome = useCallback(() => {
    handleBreadcrumbClick('/knowledge');
  }, [handleBreadcrumbClick]);

  const itemRender: BreadcrumbProps['itemRender'] = (
    currentRoute,
    params,
    items,
  ) => {
    const isLast = currentRoute?.path === items[items.length - 1]?.path;

    return isLast ? (
      <span>{currentRoute.title}</span>
    ) : (
      <span
        className={styles.breadcrumbItemButton}
        onClick={() => handleBreadcrumbClick(currentRoute.path)}
      >
        {currentRoute.title}
      </span>
    );
  };

  const actionItems: MenuProps['items'] = useMemo(() => {
    return [
      {
        key: '1',
        onClick: showFileUploadModal,
        label: (
          <div>
            <Button type="link">
              <Space>
                <FileTextOutlined />
                {t('uploadFile', { keyPrefix: 'fileManager' })}
              </Space>
            </Button>
          </div>
        ),
      },
      { type: 'divider' },
      {
        key: '2',
        onClick: showFolderCreateModal,
        label: (
          <div>
            <Button type="link">
              <Space>
                <FolderOpenOutlined />
                {t('newFolder', { keyPrefix: 'fileManager' })}
              </Space>
            </Button>
          </div>
        ),
      },
    ];
  }, [t, showFolderCreateModal, showFileUploadModal]);

  // 传递所有可见文件/文件夹，确保批量删除时能显示所有选中项名称
  const { handleRemoveFile } = useHandleDeleteFile(
    selectedRowKeys,
    setSelectedRowKeys,
    dataSource,
    isBatchMode ? onExitBatchMode : undefined // 只在批量模式下传递
  );

  const handleShowMoveFileModal = useCallback(() => {
    showMoveFileModal(selectedRowKeys);
  }, [selectedRowKeys, showMoveFileModal]);

  const disabled = selectedRowKeys.length === 0;

  const items: MenuProps['items'] = useMemo(() => {
    return [
      {
        key: '4',
        onClick: handleRemoveFile,
        label: (
          <Flex gap={10}>
            <span className="flex items-center justify-center">
              <Trash2 className="size-4" />
            </span>
            <b>{t('delete', { keyPrefix: 'common' })}</b>
          </Flex>
        ),
      },
      {
        key: '5',
        onClick: handleShowMoveFileModal,
        label: (
          <Flex gap={10}>
            <span className="flex items-center justify-center">
              <FolderInput className="size-4"></FolderInput>
            </span>
            <b>{t('move', { keyPrefix: 'common' })}</b>
          </Flex>
        ),
      },
    ];
  }, [handleShowMoveFileModal, t, handleRemoveFile]);

  // 批量操作模式时，显示批量操作栏
  if (isBatchMode) {
    return (
      <>
        {/* 返回按钮位于 filter 上方 */}
        {!isRootDirectory && (
          <div style={{ marginBottom: '8px' }}>
            <Button
              icon={<ReturnIcon />}
              onClick={handleBackToHome}
              className={styles.fileToolbarBackButton}
            >
              返回文件列表
            </Button>
          </div>
        )}
        
        <div className={styles.filter}>
          <Space>
            <div style={{ visibility: 'hidden' }}>
              <Breadcrumb items={breadcrumbItems} itemRender={itemRender} />
            </div>
          </Space>
          <Space>
            <span style={{ color: '#454545', fontSize: '14px',backgroundColor: '#f7f7f7',padding: '8px 16px',borderRadius: '8px' }}>
              {selectedRowKeys.length > 0 
                ? `已选择 ${selectedRowKeys.length} 项` 
                : '请选择要操作的文件'
              }
            </span>
            <Button 
              onClick={handleRemoveFile}
              className={styles.fileToolbarDeleteButton}
              disabled={selectedRowKeys.length === 0}
            >
              删除  
            </Button>
            <Button 
              onClick={onExitBatchMode}
              className={styles.fileToolbarCancelButton}
            >
              取消
            </Button>
          </Space>
        </div>
      </>
    );
  }

  return (
    <>
      {/* 返回按钮位于 filter 上方 */}
      {!isRootDirectory && (
        <div style={{ marginBottom: '8px' }}>
          <Button
            icon={<ReturnIcon />}
            onClick={handleBackToHome}
            className={styles.fileToolbarBackButton}
          >
            返回文件列表
          </Button>
        </div>
      )}
      
      <div className={styles.filter}>
        <Space>
          {/* 主页显示创建文件组按钮 */}
          {isRootDirectory && (
            <Button
              type="primary"
              onClick={showFolderCreateModal}
              className={styles.rootCreateFolderButton}
            >
              创建文件组
            </Button>
          )}
          
          {/* 只在二级文件夹中显示上传和创建按钮 */}
          {!isRootDirectory && (isKnowledgeBase || (
            <>
              <Button
                type="primary"
                onClick={showFileUploadModal}
                className={styles.fileToolbarUploadButton}
              >
                上传文件
              </Button>
              <Button
                onClick={showFolderCreateModal}
                className={styles.fileToolbarCreateFolderButton}
              >
                创建文件组
              </Button>
            </>
          ))}
          {!isRootDirectory && (
            <Breadcrumb items={breadcrumbItems} itemRender={itemRender} style={{ display: 'none' }}/>
          )}
        </Space>
        <Space>
          
          {/* 旧代码 */}
          {/* {isKnowledgeBase || (
            <Dropdown
              menu={{ items }}
              placement="bottom"
              arrow={false}
              disabled={disabled}
            >
              <Button>
                <Space>
                  <b> {t('bulk')}</b>
                  <DownOutlined />
                </Space>
              </Button>
          )} */}
          <span style={{ color: '#adadad', fontSize: '14px' }}>
            共 {total} 个文件
          </span>
          <Input
            // placeholder={t('searchFiles')}
            placeholder="AI搜索..."
            value={searchString}
            style={{ width: 220 }}
            allowClear
            onChange={handleInputChange}
            prefix={<SearchOutlined />}
            className={styles.fileToolbarSearchInput}
          />
          {/* 只在二级文件夹中显示批量操作按钮 */}
          {!isRootDirectory && (isKnowledgeBase || (
            <Button onClick={onEnterBatchMode} className={styles.fileToolbarBatchButton}>
              批量操作
            </Button>
          ))}
          {/* 旧代码 */}
          {/* {isKnowledgeBase || (
            <Dropdown menu={{ items: actionItems }} trigger={['click']}>
              <Button type="primary" icon={<PlusOutlined />}>
                {t('addFile')}
              </Button>
            </Dropdown>
          )} */}
          {/* 新代码 */}

        </Space>
      </div>
    </>
  );
};

export default FileToolbar;
