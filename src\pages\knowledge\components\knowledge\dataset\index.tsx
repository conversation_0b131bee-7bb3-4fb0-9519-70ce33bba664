import DocumentToolbar from './document-toolbar';
import {
  useGetRowSelection,
  useHandleWebCrawl,
  useCreateEmptyDocument,
  useHandleUploadDocument,
  useNavigateToOtherPage,
  useRenameDocument,
  useChangeDocumentParser,
  useShowMetaModal,
} from './hooks';
import { Flex, Typography, Tooltip, Switch, Tag, Checkbox, Pagination } from 'antd';
import type { IDocumentInfo } from '../interfaces/document';
import { CircleHelp } from 'lucide-react';
import { RunningStatus, DocumentParserType } from '../constants/knowledge';
import {
  useFetchNextDocumentList,
  useSetNextDocumentStatus,
} from '../hooks/document-hooks';
import { formatDate } from '@/utils/date';
import ParsingStatusCell from './parsing-status-cell';
import { useSelectParserList } from '../hooks/user-setting-hooks';
import CreateFileModal from './create-file-modal';
import FileUploadModal from '../components/file-upload-modal';
import ParsingActionCell from './parsing-action-cell';
import { useSetSelectedRecord } from '../hooks/logic-hooks';
import RenameModal from './rename-modal';
import { getExtension } from '../utils/document-util';
import ChunkMethodModal from '../components/chunk-method-modal';
import FilePicker, { FileItem } from '../components/file-picker';
import TxtSvg from '../svg/txt.svg';
import PdfSvg from '../svg/pdf.svg';
import { SetMetaModal } from './set-meta-modal';
import { useNavigate } from 'react-router-dom';
import { formatBytes } from '@/utils/formatBytes';
import { IconLeft } from '@arco-design/web-react/icon';
import { useState } from 'react'

const parsingStatusTip =
  '文本解析的时间取决于诸多因素。如果开启了知识图谱、RAPTOR、自动问题提取、自动关键词提取等功能，时间会更长。如果解析进度条长时间不更新，也可以参考这两条 FAQ：https://ragflow.io/docs/dev/faq#why-does-my-document-parsing-stall-at-under-one-percent。';

const runningStatus = ['未解析', '解析中', '取消', '成功', '失败'];

const Text = Typography.Text;

const KnowledgeFile = () => {
  const navigate = useNavigate();
  const rowSelection = useGetRowSelection();
  const parserList = useSelectParserList();
  const [visible, setVisible] = useState(false);
  const { searchString, documents, pagination, handleInputChange } = useFetchNextDocumentList();

  const {
    webCrawlUploadVisible,
    hideWebCrawlUploadModal,
    showWebCrawlUploadModal,
    onWebCrawlUploadOk,
    webCrawlUploadLoading,
  } = useHandleWebCrawl();

  const {
    documentUploadVisible,
    hideDocumentUploadModal,
    showDocumentUploadModal,
    onDocumentUploadOk,
    documentUploadLoading,
    uploadFileList,
    setUploadFileList,
    uploadProgress,
    setUploadProgress,
  } = useHandleUploadDocument();

  const {
    createLoading,
    onCreateOk,
    createVisible,
    hideCreateModal,
    showCreateModal,
  } = useCreateEmptyDocument();

  const { toChunk } = useNavigateToOtherPage();

  const { setDocumentStatus } = useSetNextDocumentStatus();

  const { currentRecord, setRecord } = useSetSelectedRecord<IDocumentInfo>();

  const {
    renameLoading,
    onRenameOk,
    renameVisible,
    hideRenameModal,
    showRenameModal,
  } = useRenameDocument(currentRecord.id);

  const {
    changeParserLoading,
    onChangeParserOk,
    changeParserVisible,
    hideChangeParserModal,
    showChangeParserModal,
  } = useChangeDocumentParser(currentRecord.id);

  const {
    showSetMetaModal,
    hideSetMetaModal,
    setMetaVisible,
    setMetaLoading,
    onSetMetaModalOk,
  } = useShowMetaModal(currentRecord.id);

  const getExtensionSvg = (name: string) => {
    const ext = getExtension(name);
    if (ext === 'txt') {
      return <TxtSvg className="size-md" />;
    } else if (ext === 'pdf') {
      return <PdfSvg className="size-md" />
    }
  };

  /**
   * 渲染单个文档行
   * @param document 文档信息
   * @param index 索引
   * @returns JSX元素
   */
  const renderDocumentRow = (document: IDocumentInfo, index: number) => {
    const ext = getExtension(document.name);
    const byte = formatBytes(document.size);
    const isSelected = rowSelection.selectedRowKeys?.includes(document.id);
    
    return (
      <div key={document.id} className="border rounded-xl border-gray-200 py-2 px-4 hover:bg-gray-50 mb-2">
        <Flex align="center" gap={16}>
          {/* 选择框和序号 */}
          <div className="flex items-center gap-2 w-16">
            {/* <Checkbox
              checked={isSelected}
              onChange={(e) => {
                const checked = e.target.checked;
                const newSelectedKeys = checked
                  ? [...(rowSelection.selectedRowKeys || []), document.id]
                  : (rowSelection.selectedRowKeys || []).filter(key => key !== document.id);
                // rowSelection.onChange?.(newSelectedKeys, [], );
              }}
            /> */}
            <span className="text-[#999] text-sm">{index + 1}</span>
          </div>
          
          {/* 文件名称 */}
          <div className="flex-1 min-w-0">
            <div className="cursor-pointer" onClick={() => toChunk(document.id)}>
              <Flex gap={10} align="center">
                {getExtensionSvg(document.name)}
                <Text ellipsis={{ tooltip: document.name }} className="!text-[#000000B8]">
                  {document.name}
                </Text>
              </Flex>
            </div>
          </div>
          
          {/* 文件类型 */}
          <div className="w-20">
            <span className="bg-[#f5f5f5] rounded-[4px] text-[#939393] text-[12px] p-[2px_6px] inline-block">
              {ext}
            </span>
          </div>
          
          {/* 文件大小 */}
          <div className="w-24">
            <span className="text-[#999] text-[12px]">{byte}</span>
          </div>
          
          {/* 分块数 */}
          <div className="w-24">
            <span className="bg-[#f5f5f5] rounded-[4px] text-[#444] text-[12px] p-[2px_6px] inline-block">
              {document.chunk_num} 分段
            </span>
          </div>
          
          {/* 解析状态 */}
          <div className="w-32">
            <ParsingStatusCell record={document} />
          </div>
          
          {/* 上传日期 */}
          <div className="w-32">
            <span className="text-[#00000066] text-[12px]">
              {formatDate(document.create_time, 'YYYY-MM-DD HH:mm')}
            </span>
          </div>
          
          {/* 操作 */}
          <div className="w-32 flex justify-end">
            <ParsingActionCell
              setCurrentRecord={setRecord}
              showRenameModal={showRenameModal}
              showChangeParserModal={showChangeParserModal}
              showSetMetaModal={showSetMetaModal}
              record={document}
            />
          </div>
        </Flex>
      </div>
    );
  };

  return (
    <>
      <div
        className="flex items-center gap-3 cursor-pointer hover:bg-gray-50 rounded-lg p-3 transition-colors duration-200 w-fit font-bold"
        onClick={(e) => {
          e.stopPropagation();
          navigate('/knowledge/knowledge');
        }}
      >
        <div className="flex items-center justify-center w-8 h-8 duration-200">
          <IconLeft />
        </div>
        <span className="text-[18px] font-medium text-[#333] transition-colors duration-200">
          文件列表
        </span>
      </div>
      <DocumentToolbar
        selectedRowKeys={rowSelection.selectedRowKeys as string[]}
        showCreateModal={showCreateModal}
        showDocumentUploadModal={showDocumentUploadModal}
        // showDocumentUploadModal={() => {setVisible(true)}}
        searchString={searchString}
        handleInputChange={handleInputChange}
        documents={documents}
        total={pagination.total}
      ></DocumentToolbar>

      {/* 文档列表 */}
      <div className="bg-white">
        {documents.map((document, index) => renderDocumentRow(document, index))}
      </div>
      
      {/* 分页 */}
      {pagination.total > 0 && (
        <div className="flex justify-end py-4">
          <Pagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            showSizeChanger={pagination.showSizeChanger}
            showQuickJumper={pagination.showQuickJumper}
            showTotal={pagination.showTotal}
            onChange={pagination.onChange}
            onShowSizeChange={pagination.onShowSizeChange}
          />
        </div>
      )}
      <CreateFileModal
        visible={createVisible}
        hideModal={hideCreateModal}
        loading={createLoading}
        onOk={onCreateOk}
      />

      <FilePicker
        visible={visible}
        onCancel={() => setVisible(false)}
        onConfirm={(selectedFiles: FileItem[]) => {
          console.log(selectedFiles)
        }}
        multiple
        title="选择文件"
      />
      <FileUploadModal
        visible={documentUploadVisible}
        hideModal={hideDocumentUploadModal}
        loading={documentUploadLoading}
        onOk={onDocumentUploadOk}
        uploadFileList={uploadFileList}
        setUploadFileList={setUploadFileList}
        uploadProgress={uploadProgress}
        setUploadProgress={setUploadProgress}
      ></FileUploadModal>

      <RenameModal
        visible={renameVisible}
        onOk={onRenameOk}
        loading={renameLoading}
        hideModal={hideRenameModal}
        initialName={currentRecord.name}
      ></RenameModal>

      <ChunkMethodModal
        documentId={currentRecord.id}
        parserId={currentRecord.parser_id as DocumentParserType}
        parserConfig={currentRecord.parser_config}
        documentExtension={getExtension(currentRecord.name)}
        onOk={onChangeParserOk}
        visible={changeParserVisible}
        hideModal={hideChangeParserModal}
        loading={changeParserLoading}
      />
      {setMetaVisible && (
        <SetMetaModal
          visible={setMetaVisible}
          hideModal={hideSetMetaModal}
          onOk={onSetMetaModalOk}
          loading={setMetaLoading}
          initialMetaData={currentRecord.meta_fields}
        ></SetMetaModal>
      )}
    </>
  );
};

export default KnowledgeFile;
